import { BodyLarge, HeadingXLarge } from "@/components/UI/Typography";
import { htmlParser } from "@/utils/htmlParser";

const htmlString = `
<h1>Heading 1</h1>
<h2>Heading 2</h2>
<h3>Heading 3</h3>
<h4>Heading 4</h4>
<h5>Heading 5</h5>
<h6>Heading 6</h6>
<p>Paragraph</p>
<span>Span</span>
<a href="#">Link</a>
<ul>
  <li>List Item 1</li>
  <li>List Item 2</li>
  <li>List Item 3</li>
</ul>
<ol>
  <li>List Item 1</li>
  <li>List Item 2</li>
  <li>List Item 3</li>
</ol>
<table>
  <tr>
    <td>Table Data 1</td>
    <td>Table Data 2</td>
  </tr>
</table>
`;

export default function(){
  return (
    <div className="p-10">
      <h1 className="text-2xl font-bold mb-4">HTML Parser Test</h1>
      <div className="border p-4">
        {htmlParser(htmlString,{
          components: {
            p: BodyLarge,
            heading: HeadingXLarge
          },
        })}
      </div>
    </div>
  )
}