
import { fetchStudioCmsData } from "@/app/api/studio-cms/apis/fetchDataCMS";
import PartnersPage from "@/components/Influencer/AllInfluencers";
import BreadcrumbsSchema from "@/components/SchemaMarkup/Breadcrumbs";
import { PageSchema } from "@/components/SchemaMarkup/PageSchema";
import { Metadata } from "next";

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: "Our Partners | OneAssure",
  description: "Discover our trusted partners and influencers who help bring you the best insurance recommendations and advice.",
  keywords: ["insurance partners", "influencers", "insurance recommendations", "OneAssure partners"],
  openGraph: {
    title: "Our Partners | OneAssure",
    description: "Discover our trusted partners and influencers who help bring you the best insurance recommendations and advice.",
    type: "website",
  },
  metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
  alternates: {
    canonical: "/partner",
  },
};

async function getAllInfluencers() {
  const query = `
    query GetAllInfluencers {
      site_influencer(where: {is_published: {_eq: true}}) {
        id
        name
        slug
        hero_image_url
        influencer_recommendations {
          designation
        }
      }
    }
  `;
  
  const operationName = "GetAllInfluencers";
  const variables = {};
  const response = await fetchStudioCmsData(query, operationName, variables);
  return response.payload.data.site_influencer;
}

export default async function PartnersListPage() {
  const influencers = await getAllInfluencers();

  const breadcrumbs = [
    { name: "OneAssure", item: `${process.env.NEXT_PUBLIC_BASE_URL}/` },
    {
      name: "Our Partners",
      item: `${process.env.NEXT_PUBLIC_BASE_URL}/partner`,
    },
  ];

  const pageUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/partner`;
  const pageContent = "Discover our trusted partners and influencers who help bring you the best insurance recommendations and advice.";

  return (
    <>
      <BreadcrumbsSchema breadcrumbs={breadcrumbs} />
      <PageSchema
        name="Our Partners"
        url={pageUrl}
        headline="Our Partners | OneAssure"
        description={pageContent}
        inLanguage="en"
        mainEntity={{
          name: "Our Partners",
          type: "WebPage",
        }}
        publisher={{
          name: "OneAssure",
          logoUrl: `${process.env.NEXT_PUBLIC_BASE_URL}/logo.png`,
        }}
      />
      <PartnersPage influencers={influencers} />
    </>
  );
}

