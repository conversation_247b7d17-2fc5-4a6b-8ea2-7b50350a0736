import InsuranceAllProducts from "@/components/globals/InsuranceAllProducts";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "",
  description: "",
  keywords: [],
  openGraph: {
    title: "",
    description: "",
  },
  metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
  alternates: {
    canonical: "/term-insurance",
  },
};

async function getCompanyData() {
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_CLIENT_API}/academy/slugs/v1/?category=term`
  ).then((res) => res.json());
  return res;
}

async function getTestimonials() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/personal-home-landing-page?populate[testimonials][populate][testimonial][fields][0]=name&populate[testimonials][populate][testimonial][fields][1]=statement&populate[testimonials][populate][testimonial][fields][2]=backgroundColor&populate[testimonials][populate][testimonial][populate][thumbnail][fields][0]=url`,
    { headers }
  ).then((res) => res.json());
  return res;
}

export default async function HealthInsurancePage() {
  const compData = await getCompanyData();
  const testimonials = await getTestimonials();

  return (
    <InsuranceAllProducts
      companyData={compData}
      testimonials={testimonials.data.attributes.testimonials}
      category={"term"}
    />
  );
}
