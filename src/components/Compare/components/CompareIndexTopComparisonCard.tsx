import { Button } from "@/components/UI/Button";
import { CompareIndexPageTopComparisonCards } from "@/components/Compare/type";
import {
  BodySmall,
  HeadingMedium,
  HeadingSmall,
} from "@/components/UI/Typography";
import Image from "next/image";
import Link from "next/link";

export default function CompareIndexTopComparisonCard({
  topComparison,
}: {
  topComparison: CompareIndexPageTopComparisonCards;
}) {
  if (!topComparison) return null;

  const comparisonUrl = `/compare-health-insurance-plans/${topComparison.variant_one.product.insurer.slug}-${topComparison.variant_one.variant_slug}-vs-${topComparison.variant_two.product.insurer.slug}-${topComparison.variant_two.variant_slug}/${topComparison.variant_one.id}-${topComparison.variant_two.id}`;

  return (
    <div className="flex flex-col items-center justify-center rounded-xl border border-primary-200 h-full">
      <div className="flex flex-col items-center gap-4 p-6 h-full">
        <div className="flex flex-col items-center justify-between gap-4 h-full">
          <div className="flex flex-col items-center justify-center gap-3">
            <div className="relative w-20 h-10 bg-white rounded shadow p-1">
              <img src={topComparison.variant_one.product.insurer.logo_url} alt={topComparison.variant_one.product.insurer.slug} className="object-contain w-full h-full"/>
            </div>
            <HeadingSmall className="text-center font-medium text-neutral-1100" as="h3">
              {topComparison.variant_one.variant_name}
            </HeadingSmall>
          </div>
          <div className="text-neutral-1100 bg-primary-200 rounded-full p-2">
            <BodySmall>VS</BodySmall>
          </div>
          <div className="flex flex-col items-center justify-center gap-3">
            <div className="relative w-20 h-10 bg-white rounded shadow p-1">
              <img src={topComparison.variant_two.product.insurer.logo_url} alt={topComparison.variant_two.product.insurer.slug} className="object-contain w-full h-full"/>
            </div>
            <HeadingSmall className="text-center font-medium text-neutral-1100" as="h3">
              {topComparison.variant_two.variant_name}
            </HeadingSmall>
          </div>
        </div>
        <div className="flex items-center justify-center">
					<Link href={comparisonUrl}><Button variant="primary">View Comparison</Button></Link>
        </div>
      </div>
    </div>
  );
}
