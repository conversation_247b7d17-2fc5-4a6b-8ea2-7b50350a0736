import React, { useState } from "react";
import { IoMdCheckmarkCircleOutline } from "react-icons/io";
import Image from "next/image";
import Link from "next/link";
import { BodyLarge, HeadingMedium } from "@/components/UI/Typography";

export type HeroCard = {
  slug: string;
  variant_slug: string;
  logo: string;
  alt: string;
  title: string;
  subtitle: string;
  whatMakesItSpecial: string;
  bestFor: string[];
};

const Card: React.FC<{ card: HeroCard }> = ({ card }) => {
  const [showFullText, setShowFullText] = useState(false);

  const wordCount = card.whatMakesItSpecial.split(" ").length;
  const shouldShowSeeMore = wordCount > 50;

  const displayText =
    shouldShowSeeMore && !showFullText
      ? card.whatMakesItSpecial.split(" ").slice(0, 50).join(" ") + "..."
      : card.whatMakesItSpecial;

  return (
    <div className="flex-1 border border-primary-300 rounded-xl w-full shadow-md bg-white flex flex-col">
      {/* Header */}
      <div className="flex items-center w-full gap-5 px-4 md:px-5 py-3 bg-primary-100 rounded-t-xl">
        <div className="relative w-20 h-10 bg-white rounded shadow p-1">
          <Link
            scroll={false}
            href={`${process.env.NEXT_PUBLIC_BASE_URL}/health-insurance/${card.slug}-health-insurance`}
          >
            <Image
              src={card.logo}
              alt={card.alt}
              fill
              className="object-contain"
            />
          </Link>
        </div>
        <div>
          <HeadingMedium weight="semibold" className="text-primary-800">
            <Link
              scroll={false}
              href={`${process.env.NEXT_PUBLIC_BASE_URL}/health-insurance/${card.slug}-health-insurance/${card.variant_slug}`}
            >
              {card.title}
            </Link>
          </HeadingMedium>
          <BodyLarge className="text-neutral-1100">{card.subtitle}</BodyLarge>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 flex flex-col justify-between p-4 md:px-5 md:py-4">
        <BodyLarge weight="semibold" className="text-neutral-1100 mb-2">
          What Makes It Special:
        </BodyLarge>
        <div className="mb-3">
          <BodyLarge className="text-neutral-800 text-justify">
            {displayText}
          </BodyLarge>
          {shouldShowSeeMore && (
            <button
              onClick={() => setShowFullText(!showFullText)}
              className="text-primary-800 text-base font-medium ml-1.5 hover:underline focus:outline-none"
            >
              {showFullText ? "See less" : "See more"}
            </button>
          )}
        </div>
        <BodyLarge weight="semibold" className="text-neutral-1100 mb-2">
          Best For:
        </BodyLarge>
        <ul className="space-y-2">
          {card.bestFor?.length === 0 ? (
            <li>Not available</li>
          ) : (
            card.bestFor?.map((item, index) => (
              <li className="flex items-center gap-2" key={index}>
                <IoMdCheckmarkCircleOutline
                  size={16}
                  className="text-green-500 flex-shrink-0"
                />
                <BodyLarge className="text-neutral-800">{item}</BodyLarge>
              </li>
            ))
          )}
        </ul>
      </div>
    </div>
  );
};

export default Card;
