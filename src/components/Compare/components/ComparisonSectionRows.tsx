import React from "react";
import Image from "next/image";
import { htmlRenderer } from "@/utils/htmlRenderer";
import { GoDownload } from "react-icons/go";
import SectionHeader from "@/components/globals/SectionHeader";
import Separator from "./Separator";
import Link from "next/link";
import {
  BodyLarge,
  BodyMedium,
  HeadingMedium,
} from "@/components/UI/Typography";
import parse from "html-react-parser";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import InfoIcon from "@/components/globals/DSComponentsV0/InfoIcon";

// Types
type FeatureValue = {
  value?: string;
  sub_value?: string;
  compare_feature: {
    name?: string;
    hint_text?: string;
  };
};

type Insurer = {
  slug?: string;
  logo_url?: string;
  network_hospital_count?: number;
  claim_settlement_ratio?: number;
};

type Product = {
  insurer?: Insurer;
  name?: string;
  policy_brochure_url?: string;
  policy_wording_url?: string;
};

type HealthProductVariant = {
  slug?: string;
  variant_slug?: string;
  name?: string;
  subtitle?: string;
  id?: string;
  variant_name?: string;
  feature_values?: FeatureValue[];
  product?: Product;
};

type ComparisonSectionRowsProps = {
  health_product_variants: HealthProductVariant[];
  heading?: string;
  subheading?: string;
};

// Download Link Component
const DownloadLink: React.FC<{
  url?: string;
  label: string;
  icon: React.ReactNode;
  className?: string;
}> = ({ url, label, icon, className = "" }) => {
  if (url) {
    return (
      <BodyMedium className="text-primary-700 hover:underline">
        <Link
          href={url}
          target="_blank"
          rel="noopener noreferrer"
          className={`flex items-center gap-2`}
        >
          {icon}
          {label}
        </Link>
      </BodyMedium>
    );
  }

  return (
    <button
      disabled
      className={`flex items-center gap-2 rounded-xl text-gray-400 text-sm cursor-not-allowed opacity-50 ${className}`}
    >
      {icon}
      {label}
    </button>
  );
};

// Plan Feature Card Component
const PlanFeatureCard: React.FC<{ variant: HealthProductVariant }> = ({
  variant,
}) => (
  <div className="bg-white w-full rounded-2xl px-6 py-4 flex flex-col justify-between border border-primary-300">
    <div className="flex flex-row items-center gap-5 mb-2">
      <div className="bg-white rounded-lg flex items-center justify-center">
        <div className="relative w-20 h-10">
          <Image
            src={variant.product?.insurer?.logo_url || ""}
            alt={variant.variant_name || "Health Insurance"}
            fill
            className="object-contain"
          />
        </div>
      </div>
      <div className="flex flex-col">
        <HeadingMedium weight="semibold" className="text-neutral-1100">
          {variant.variant_name}
        </HeadingMedium>
        <BodyMedium className="text-neutral-1100">
          {variant.subtitle || "Health Insurance Plan"}
        </BodyMedium>
      </div>
    </div>

    <div className="flex flex-row justify-end gap-6">
      <DownloadLink
        url={variant.product?.policy_brochure_url}
        label="Brochure"
        icon={<GoDownload />}
      />
      <DownloadLink
        url={variant.product?.policy_wording_url}
        label="Policy Wording"
        icon={<GoDownload />}
      />
    </div>
  </div>
);

// Feature Content Component
const FeatureContent: React.FC<{ value: string | null | undefined, sub_value: string | null | undefined }> = ({
  value,
  sub_value,
}) => {
  if (!value) {
    return <div className="text-left text-gray-500">Not Available</div>;
  }

  return (
    <div className="text-left">
      <div className="list-disc list-outside space-y-1 pl-4">
        <div className="text-neutral-800">
          {parse(value, { replace: htmlRenderer })}
        </div>
        {sub_value && (
          <div className="text-neutral-800">
            {parse(sub_value, { replace: htmlRenderer })}
          </div>
        )}
      </div>
    </div>
  );
};

// Mobile Plan Card Component
const MobilePlanCard: React.FC<{
  variant: HealthProductVariant;
  index: number;
  totalVariants: number;
}> = ({ variant, index, totalVariants }) => (
  <React.Fragment>
    <div className="bg-white rounded-xl p-4 flex flex-col justify-between border border-primary-300">
      <div className="flex flex-row items-center gap-3 mb-3">
        <div className="bg-white rounded-lg flex items-center justify-center">
          <div className="relative w-20 h-10">
            <Image
              src={variant.product?.insurer?.logo_url || ""}
              alt={variant.variant_name || "Health Insurance"}
              fill
              className="object-contain"
            />
          </div>
        </div>
        <div className="flex flex-col">
          <div className="text-xl font-poppins font-semibold text-left text-neutral-1100">
            {variant.variant_name}
          </div>
          <p className="text-left text-sm font-normal text-neutral-1100">
            {variant.subtitle || "Health Insurance Plan"}
          </p>
        </div>
      </div>

      <div className="flex flex-row justify-between mx-3">
        <DownloadLink
          url={variant.product?.policy_brochure_url}
          label="Brochure"
          icon={<GoDownload />}
        />
        <DownloadLink
          url={variant.product?.policy_wording_url}
          label="Policy Wording"
          icon={<GoDownload />}
        />
      </div>
    </div>
    {index < totalVariants - 1 && (
      <div className="flex justify-center items-center my-3 w-full">
        <Separator />
      </div>
    )}
  </React.Fragment>
);

// Desktop Feature Section Component
const DesktopFeatureSection: React.FC<{
  section: {
    section: string;
    hint_text?: string;
    features: Array<{
      feature: string;
      items: Array<{ variant_id: string; value: string | null; sub_value: string | null }>;
    }>;
  };
  health_product_variants: HealthProductVariant[];
}> = ({ section, health_product_variants }) => (
  <div className="border border-primary-200 rounded-2xl mb-6 bg-white overflow-hidden">
    <table className="w-full">
      <thead>
        <tr>
          <th
            colSpan={health_product_variants.length}
            className="bg-primary-100 px-6 py-2 text-left"
          >
            <div className="flex items-center gap-2">
              <BodyLarge weight="bold" className="text-neutral-1100">
                {section.section}
              </BodyLarge>
              <InfoIcon hintText={section.hint_text} />
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr>
          {health_product_variants.map((variant, index) => {
            const featureItem = section.features[0].items.find(
              (item) => item.variant_id === variant.id
            );
            const value = featureItem?.value;
            const sub_value = featureItem?.sub_value;
            return (
              <td
                key={variant.id}
                className={`w-1/3 px-6 py-4 ${
                  index > 0 ? "border-l border-primary-300 pl-6" : "pr-6"
                }`}
              >
                <div className="flex flex-col items-start gap-2">
                  <div className="flex items-center gap-4">
                    <BodyLarge weight="semibold" className="text-neutral-1100">
                      {variant.variant_name}
                    </BodyLarge>
                  </div>
                  <FeatureContent value={value} sub_value={sub_value} />
                </div>
              </td>
            );
          })}
        </tr>
      </tbody>
    </table>
  </div>
);

// Mobile Feature Section Component
const MobileFeatureSection: React.FC<{
  section: {
    section: string;
    hint_text?: string;
    features: Array<{
      feature: string;
      items: Array<{ variant_id: string; value: string | null; sub_value: string | null }>;
    }>;
  };
  health_product_variants: HealthProductVariant[];
}> = ({ section, health_product_variants }) => (
  <div key={section.section} className="mb-4">
    {section.features.map((feature) => (
      <div
        key={feature.feature}
        className="border border-primary-300 rounded-xl overflow-hidden"
      >
        <table className="w-full">
          <thead>
            <tr>
              <th className="flex items-start justify-start text-primary-700 gap-2 px-4 sticky top-[4.5rem] bg-primary-100 text-base font-poppins font-semibold py-2 z-5 border-b border-primary-100 text-left">
                <span>{feature.feature}</span>
                <InfoIcon hintText={section.hint_text} />
              </th>
            </tr>
          </thead>
          <tbody className="space-y-2">
            {health_product_variants.map((variant, variantIndex) => {
              const featureItem = feature.items.find(
                (item) => item.variant_id === variant.id
              );
              const subValue = featureItem?.sub_value;
              return (
                <React.Fragment key={variant.id}>
                  <tr>
                    <td className="flex items-start p-4 bg-white">
                      <div className="flex-1">
                        <p className="text-sm text-neutral-1100 font-bold mb-2">
                          {variant.variant_name}
                        </p>
                        {featureItem && featureItem.value ? (
                          <div className="text-neutral-800 text-sm">
                            <div className="list-disc ml-5 space-y-1">
                              <div className="text-left leading-relaxed">
                                {parse(featureItem.value, {
                                  replace: htmlRenderer,
                                })}
                              </div>
                              {subValue && (
                                <div className="text-left leading-relaxed">
                                  {parse(subValue, { replace: htmlRenderer })}
                                </div>
                              )}
                            </div>
                          </div>
                        ) : (
                          <p className="text-left text-gray-500 text-sm">
                            Not Available
                          </p>
                        )}
                      </div>
                    </td>
                  </tr>
                  {variantIndex < health_product_variants.length - 1 && (
                    <tr>
                      <td className="flex justify-center items-center my-0">
                        <Separator />
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              );
            })}
          </tbody>
        </table>
      </div>
    ))}
  </div>
);

const ComparisonSectionRows: React.FC<ComparisonSectionRowsProps> = ({
  health_product_variants,
  heading = "Detailed Features Comparison",
  subheading = "Compare the key features of different health insurance plans",
}) => {
  // Group features by their compare_feature.name to create sections
  const featureGroups = React.useMemo(() => {
    const allFeatures = new Map<string, string>();

    // Collect all unique feature names and their hint_text
    health_product_variants.forEach((variant) => {
      variant.feature_values?.forEach((feature) => {
        const featureName = feature.compare_feature?.name || "";
        const hintText = feature.compare_feature?.hint_text || "";
        if (featureName && !allFeatures.has(featureName)) {
          allFeatures.set(featureName, hintText);
        }
      });
    });

    // Create sections with features
    const sections = Array.from(allFeatures.entries()).map(([featureName, hintText]) => ({
      section: featureName,
      hint_text: hintText,
      features: [
        {
          feature: featureName,
          hint_text: hintText,
          items: health_product_variants.map((variant) => {
            const featureValue = variant.feature_values?.find(
              (fv) => fv.compare_feature?.name === featureName
            );
            return {
              variant_id: variant.id || "",
              value: featureValue?.value || null,
              sub_value: featureValue?.sub_value || null,
            };
          }),
        },
      ],
    }));

    return sections;
  }, [health_product_variants]);

  return (
    <SectionContainerLarge className="comparison-main-section" id="comparison">
      {/* Desktop View */}
      <div className="scroll-m-28 hidden md:block">
        <SectionHeader
          pill="Insurance Plans Comparison"
          heading={heading}
          subheading={subheading}
          component="h2"
        />

        {/* Plan Features Header */}
        <div className="flex flex-row items-center justify-between gap-5 mb-6">
          {health_product_variants.map((variant, idx) => (
            <React.Fragment key={variant.id}>
              <PlanFeatureCard variant={variant} />
              {idx < health_product_variants.length - 1 && (
                <div className="flex items-center justify-center">
                  <BodyMedium
                    weight="medium"
                    className="bg-primary-100 text-neutral-1100 rounded-full w-10 h-10 flex items-center justify-center shadow"
                  >
                    VS
                  </BodyMedium>
                </div>
              )}
            </React.Fragment>
          ))}
        </div>

        {/* Dynamic Feature Rows */}
        {featureGroups.map((section) => (
          <DesktopFeatureSection
            key={section.section}
            section={section}
            health_product_variants={health_product_variants}
          />
        ))}
      </div>

      {/* Mobile View */}
      <div className="md:hidden mt-5 mb-8 scroll-m-28" id="coverage-plans">
        <SectionHeader
          pill="Insurance Plans Comparison"
          heading={heading}
          subheading={subheading}
          component="h2"
        />

        {/* Side-by-side Comparison Card for Mobile */}
        <table className="mb-4 w-full">
          <tbody>
            {health_product_variants.map((variant, index) => (
              <tr key={variant.id}>
                <td className="w-full">
                  <MobilePlanCard
                    variant={variant}
                    index={index}
                    totalVariants={health_product_variants.length}
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {/* Dynamic Sections for Mobile */}
        {featureGroups.map((section) => (
          <MobileFeatureSection
            key={section.section}
            section={section}
            health_product_variants={health_product_variants}
          />
        ))}
      </div>
    </SectionContainerLarge>
  );
};

export default ComparisonSectionRows;
