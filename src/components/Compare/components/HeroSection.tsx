"use client";
import React from "react";
import { usePathname } from "next/navigation";
import Card from "@/components/Compare/components/Card";
import PageTopBar from "@/components/globals/PageTopBar";
import SectionHeader from "@/components/globals/SectionHeader";
import SectionContainer from "@/components/globals/SectionContainer";
import Separator from "@/components/Compare/components/Separator";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import SectionContainerMedium from "@/components/globals/SectionContainerMedium";

type HeroCard = {
  slug: string;
  variant_slug: string;
  logo: string;
  alt: string;
  title: string;
  subtitle: string;
  whatMakesItSpecial: string;
  bestFor: string[];
};

type HeroSectionProps = {
  data: {
    pill: string;
    heading: string;
    subheading: string;
    cards: HeroCard[];
  };
};

const HeroSection: React.FC<HeroSectionProps> = ({ data }) => {
  const pathname = usePathname();
  // Generate breadcrumb path from URL, e.g. /health-insurance/compare -> ["health-insurance", "compare"]
  // Filter out ID-like segments and add One<PERSON><PERSON> as first item
  const pathSegments = pathname.split("/").filter(Boolean);
  const filteredSegments = pathSegments.filter((segment) => {
    // Filter out segments that look like IDs (numbers, UUIDs, etc.)
    return (
      !/^\d+$/.test(segment) &&
      !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
        segment
      )
    );
  });

  const breadcrumbPath = [
    "OneAssure",
    "Health Insurance",
    "Compare Health Insurance Plans",
    `${data.cards[0].title} vs ${data.cards[1].title}`,
  ];

  // Get the last segment (dynamic route) for the full URL
  const lastSegment = filteredSegments[filteredSegments.length - 1];
  const fullUrl = lastSegment ? `${pathname}` : pathname;

  return (
    <SectionContainer className="flex flex-col items-center mt-6 md:mt-8">
      {/* Breadcrumb and Social Share Buttons */}
      <div className="w-full hidden md:block">
        <PageTopBar breadcrumbPath={breadcrumbPath} fullUrl={fullUrl} />
      </div>
      <SectionContainerLarge className="!px-0 md:!px-0 !mb-0 md:!mb-0">
        <SectionHeader
          pill={data.pill}
          heading={data.heading}
          subheading={data.subheading}
          component="h1"
        />
        {/* Cards Row */}
        <SectionContainerMedium className="flex flex-col md:flex-row gap-3 md:gap-5 items-stretch !mb-0 md:!mb-0 !px-0 md:!px-0">
          {data.cards.map((card, idx) => (
            <React.Fragment key={card.title}>
              <Card card={card} />
              {idx === 0 && (
                <div className="flex items-center justify-center">
                  <Separator />
                </div>
              )}
            </React.Fragment>
          ))}
        </SectionContainerMedium>
      </SectionContainerLarge>
    </SectionContainer>
  );
};

export default HeroSection;