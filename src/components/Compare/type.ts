export type CompareIndexPageSectionPoints = {
  id: string;
  title: string;
  description: string;
};

export type CompareIndexPageSections = {
  id: string;
  title: string;
  description: string;
  type:
    | "assess_healthcare_need"
    | "read_inclusion_exclusion"
    | "review_claim_settlement_ratio"
    | "policy_conditions"
    | "mistakes_to_avoid"
    | "why_chose_our_expert"
    | "what_experts_help_you_with"
    | "faqs";
  compare_index_page_section_points: CompareIndexPageSectionPoints[];
};

export type CompareIndexPageFeaturesToConsiderPoints = {
  id: string;
  title: string;
  type: "benefits" | "limitations";
  points: string[];
};

export type CompareIndexPageFeaturesToConsider = {
  id: string;
  title: string;
  description: string;
  compare_index_page_features_to_consider_points: CompareIndexPageFeaturesToConsiderPoints[];
};

export type CompareIndexPageInsuranceCategoryCards = {
  id: string;
  title: string;
  points: string[];
  button_text: string;
};

export type CompareIndexPageInsuranceCategory = {
  id: string;
  title: string;
  pill_content: string;
  description: string;
  compare_index_page_insurance_category_cards: CompareIndexPageInsuranceCategoryCards[];
};

export type CompareIndexPageTopComparisonCards = {
  id: string;
  variant_one_id: string;
  variant_two_id: string;
  variant_one: {
    id: string;
    variant_name: string;
    variant_slug: string;
    product: {
      insurer: {
        logo_url: string;
        slug: string;
      };
    };
  };
  variant_two: {
    id: string;
    variant_name: string;
    variant_slug: string;
    product: {
      insurer: {
        logo_url: string;
        slug: string;
      };
    };
  };
};

export type CompareIndexPageTopComparisons = {
  id: string;
  title: string;
  description: string;
  compare_index_page_top_comparison_cards: CompareIndexPageTopComparisonCards[];
};

export type CompareIndexPageSEO = {
  id: string;
  meta_title: string;
  meta_description: string;
  prevent_indexing: boolean;
  keywords: string;
  canonical: string;
};

export type CompareIndexPageFaqs = {
  id: string;
  title: string;
  description: string;
  pill: string;
  faqs: {
      id: string;
      question: string;
      answer: string;
    }[];
};

export type CompareIndexPageAPIResponse = {
  id: string;
  hero_title: string;
  hero_description: string;
  note: string;
  pill_content: string;
  need_to_compare_title: string;
  need_to_compare_description: string;
  compare_index_page_sections: CompareIndexPageSections[];
  compare_index_page_features_to_considers: CompareIndexPageFeaturesToConsider[];
  compare_index_page_insurance_categories: CompareIndexPageInsuranceCategory[];
  compare_index_page_top_comparisons: CompareIndexPageTopComparisons[];
  compare_index_page_seo: CompareIndexPageSEO;
};

export type CompareIndexPageData = {
  staticContent: {
    hero_title: string;
    hero_description: string;
    note: string;
    pill_content: string;
    need_to_compare_title: string;
    need_to_compare_description: string;
  };
  sections: {
    id: string;
    title: string;
    description: string;
    type:
      | "assess_healthcare_need"
      | "read_inclusion_exclusion"
      | "review_claim_settlement_ratio"
      | "policy_conditions"
      | "mistakes_to_avoid"
      | "why_chose_our_expert"
      | "what_experts_help_you_with"
      | "faqs";
    compare_index_page_section_points: {
      id: string;
      title: string;
      description: string;
    }[];
  }[];
  featuresToConsider: {
    id: string;
    title: string;
    description: string;
    compare_index_page_features_to_consider_points: {
      id: string;
      title: string;
      points: string[];
      type: "benefits" | "limitations";
    }[];
  }[];
  insuranceCategories: {
    id: string;
    title: string;
    pill_content: string;
    description: string;
    compare_index_page_insurance_category_cards: {
      id: string;
      title: string;
      points: string[];
      button_text: string;
    }[];
  }[];
  topComparisons: {
    id: string;
    title: string;
    description: string;
    compare_index_page_top_comparison_cards: {
      id: string;
      variant_one_id: string;
      variant_two_id: string;
      variant_one: {
        id: string;
        variant_name: string;
        variant_slug: string;
        product: {
          insurer: {
            logo_url: string;
            slug: string;
          };
        };
      };
      variant_two: {
        id: string;
        variant_name: string;
        variant_slug: string;
        product: {
          insurer: {
            logo_url: string;
            slug: string;
          };
        };
      };
    }[];
  }[];
  pageNavigationSection: {
    activeTab: string;
    tabs: { label: string; id: string }[];
  };
  seo: CompareIndexPageSEO;
  faqData: CompareIndexPageFaqs;
};
