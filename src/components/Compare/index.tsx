"use client";
import React, { useState } from "react";
import Container from "../globals/Container";
import unifiedDataTransformer from "./Dtos/unifiedDataTransformer";
import HeroSection from "./components/HeroSection";
import QuickDecisionGuide from "./components/QuickDecisionGuide";
import ComparisonSectionRows from "./components/ComparisonSectionRows";
import WhyChooseExpertConsultationSection from "./components/WhyChooseExpertConsultationSection";
import WhatOurExpertsHelpYouWith from "./components/WhatOurExpertsHelpYouWith";
import FeaturesComparisonSection from "./components/FeaturesComparisonSection";
import { featuresComparisonData } from "./data/featuresComparisonData";
import SectionNavigationBar from "./components/SectionNavigationBar";
import CompareFAQSection from "./components/CompareFAQSection";
import MostPopularPlans from "./components/MostPopularPlans";
import InsuranceProvidersSection from "./components/InsuranceProvidersSection";
import RelatedBlogs from "./components/RelatedBlogs";
import LeadForm from "../globals/LeadForm";
import MobileBreadcrumbs from "./components/MobileBreadcrumbs";
import SharePageFloater from "@/components/globals/SharePageFloater";
import GoToTopFloater from "@/components/globals/GoToTopFloater";
import SectionContainerSmall from "../globals/SectionContainerSmall";
import CalendlyFloater from "@/components/globals/CalendlyFloater";
import CategoryCards from "../globals/CategoryCards";

// Types
type HealthProductVariant = {
  slug?: string;
  variant_slug?: string;
  name?: string;
  subtitle?: string;
  id?: string;
  variant_name?: string;
  feature_values?: Array<{
    value?: string;
    compare_feature: {
      name?: string;
    };
  }>;
  product?: {
    insurer?: {
      slug?: string;
      logo_url?: string;
      network_hospital_count?: number;
      claim_settlement_ratio?: number;
    };
    name?: string;
    policy_brochure_url?: string;
    policy_wording_url?: string;
  };
};

const ComparisonPage = ({
  data,
  popularPlans,
  allInsurerData,
  blogData,
}: {
  data?: any;
  popularPlans?: any[];
  allInsurerData?: any[];
  blogData?: {
    heading: string;
    blogs: Array<{
      title: string;
      date: string;
      author: string;
      description: string;
      imageUrl: string;
      url: string;
    }>;
  };
}) => {
  // Add state for active tab - moved before early return to follow Rules of Hooks
  const [activeTab, setActiveTab] = useState("quick-decision-guide");

  if (
    !data ||
    !data.health_product_variants ||
    !data.health_product_variants.length
  ) {
    return (
      <Container navSpacing={false} className="font-matter">
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-800 mb-4">
              No Data Available
            </h1>
            <p className="text-gray-600">
              Please try again later or contact support.
            </p>
          </div>
        </div>
      </Container>
    );
  }

  const transformedData = unifiedDataTransformer(data);
  const {
    heroData,
    quickDecisionGuideData,
    whyChooseExpertConsultationData,
    whatOurExpertsHelpYouWithData,
    faqData,
    updatedVariants,
  } = transformedData;

  // Add a handler for tab clicks that scrolls to the section
  const handleTabClick = (tab: string) => {
    setActiveTab(tab);
    const section = document.getElementById(tab);
    if (section) {
      section.scrollIntoView({ behavior: "smooth" });
    }
  };

  // Transform popular plans data to match component expectations
  const finalPlansData =
    popularPlans?.map((plan) => ({
      name: plan.name,
      logo: plan.logo,
      link: plan.url,
      popular: plan.popular,
    })) || [];

  return (
    <div className="font-poppins">
      {/* Hero Section with Cards */}
      {heroData && <HeroSection data={heroData} />}

      {/* Section Navigation Bar */}
      <SectionNavigationBar
        activeTab={activeTab}
        setActiveTab={handleTabClick}
      />

      {/* Personalized Advice Form Section */}
      <LeadForm
        pill="Insurance Plans Comparison"
        title="Get Personalized Advice"
        description="Our insurance experts are here to help you make the right choice. Get personalized recommendations based on your specific needs and budget."
      />

      {/* Quick Decision Guide Section */}
      {quickDecisionGuideData && (
        <QuickDecisionGuide data={quickDecisionGuideData} />
      )}

      {/* Comparison Section Rows */}

      {updatedVariants && updatedVariants.length > 0 ? (
        <ComparisonSectionRows
          health_product_variants={updatedVariants as HealthProductVariant[]}
        />
      ) : (
        <div
          id="comparison"
          className="w-full bg-white rounded-3xl shadow-md p-8 text-center"
        >
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">
            Compare Plans
          </h2>
          <p className="text-gray-600">
            No plans available for comparison at the moment.
          </p>
        </div>
      )}

      {/* Expert Consultation Section */}
      <LeadForm
        pill="Insurance Plans Comparison"
        title="Still Confused? Get Expert Advice"
        description="Our insurance experts are here to help you make the right choice. Get personalized recommendations based on your specific needs and budget."
        id="expert-consultation"
      />

      {/* Why Choose Our Expert Consultation Section */}
      {whyChooseExpertConsultationData && (
        <WhyChooseExpertConsultationSection
          {...whyChooseExpertConsultationData}
        />
      )}

      {/* What Our Experts Help You With Section */}
      {whatOurExpertsHelpYouWithData && (
        <WhatOurExpertsHelpYouWith {...whatOurExpertsHelpYouWithData} />
      )}

      {/* Features Comparison Section */}
      {featuresComparisonData && (
        // <FeaturesComparisonSection
        //   pill="Insurance Plans Comparison"
        //   heading="Explore Insurance Category"
        //   subHeading="Discover the right insurance plan with our easy category guide"
        //   plans={featuresComparisonData.plans}
        // />
        <CategoryCards
          pill="Insurance Plans Comparison"
          heading="Explore Insurance Category"
          subHeading="Discover the right insurance plan with our easy category guide"
          categories={featuresComparisonData.plans}
        />
      )}

      {/* FAQ Section (new, after QuickLinks) */}
      {faqData && faqData.faqs.length > 0 && (
        <CompareFAQSection
          pill={faqData.pill}
          heading={faqData.heading}
          subheading={faqData.subheading}
          faqs={faqData.faqs}
        />
      )}

      {/* Most Popular Plans Section */}
      {finalPlansData.length > 0 && (
        <MostPopularPlans
          heading="Explore other Most Popular Plans"
          plans={finalPlansData}
        />
      )}

      {/* Insurance Providers Section */}
      {allInsurerData && allInsurerData.length > 0 && (
        <InsuranceProvidersSection allInsurerData={allInsurerData} />
      )}

      {/* Related Blogs Section */}
      {blogData && blogData.blogs.length > 0 && (
        <RelatedBlogs blogData={blogData} />
      )}

      {heroData && <MobileBreadcrumbs data={heroData.cards} />}

      <SharePageFloater />
      <CalendlyFloater/>
      <GoToTopFloater />
    </div>
  );
};

export default ComparisonPage;