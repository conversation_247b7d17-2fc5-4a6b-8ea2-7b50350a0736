"use client";

import Hero from "./components/Hero";
import WhyUs from "@/components/globals/WhyUs";
import Testimonials from "@/components/globals/Testimonials";
import { GroupLPResponse } from "./type";
import { useState } from "react";
import { Suspense } from "react";
import Modal from "@/components/globals/Modal";
import GroupBenefit from "./components/GroupBenefit";
import DigitalDashboard from "./components/DigitalDashboard";

const GroupHealthLife = ({ data }: { data: GroupLPResponse }) => {
  const [openModal, setOpenModal] = useState(false);

  return (
    <>
      <Hero data={data.attributes.hero} setOpenModal={setOpenModal} />
      <DigitalDashboard data={data.attributes.digitalDashboard} />
      <GroupBenefit data={data.attributes.grpBenefit} />
      <WhyUs whyUs={data.attributes.whyUs} setOpenModal={setOpenModal} />
      {/* <Faq faqs={data.attributes.faqs} /> */}
      <Testimonials testimonials={data.attributes.testimonials.testimonial} />
      <Suspense>
        <Modal
          open={openModal}
          handleModal={() => setOpenModal(false)}
          msg="Looking for insurance purchase assistance. Thank you!"
          utmSrc="Group Health_LP"
        />
      </Suspense>
    </>
  );
};

export default GroupHealthLife;
