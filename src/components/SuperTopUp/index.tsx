"use client";
import React, { useState } from "react";
import Hero from "@/components/Insurer/component/Hero";
import ExpertReview from "@/components/Insurer/component/ExpertReview";
import PlansSection from "@/components/globals/DSComponentsV0/PlansSection";
import CardContainer from "@/components/StandalonePage/components/CardContainer";
import Testimonial from "@/components/globals/Testimonial";
import CategoryCards from "@/components/globals/CategoryCards";
import ClaimTypes from "@/components/Insurer/component/ClaimTypes";
import LeadForm from "@/components/globals/LeadForm";
import FAQs from "@/components/globals/AccordianSection";
import PageNavigation from "@/components/globals/PageNavigation";
import RelatedBlogs from "@/components/globals/RelatedBlogs";
import InclusionExclusionSection from "@/components/StandalonePage/components/InclusionExclusionSection";
import { SuperTopUpData } from "@/components/SuperTopUp/dto";
import InsurerPlan from "../globals/InsurerPlan";
import RenewalTypes from "../Insurer/component/RenewalTypes";
import AccordianSection from "@/components/globals/AccordianSection";

const SuperTopUp = ({
  data,
  allInsurerData,
  blogData,
}: {
  data: SuperTopUpData;
  allInsurerData?: any[];
  blogData?: {
    heading: string;
    blogs: Array<{
      title: string;
      date: string;
      author: string;
      description: string;
      imageUrl: string;
      url: string;
    }>;
  };
}) => {
  const [activeTab, setActiveTab] = useState("expert-review");
  
  if (!data) {
    return null;
  }

  const {
    HeroSection,
    verdictSection,
    aboutSection,
    benefitsSection,
    highlightsSection,
    uniqueFeaturesSection,
    eligiblityCriteriaSection,
    factorsToConsiderSection,
    pageNavigationSection,
    inclusionSection,
    insuranceCategorySection,
    renewalSection,
    howToBuySection,
    faqSection,
    claimSettlementSection,
    testimonialSection,
    healthInsurer,
  } = data;

  
  return (
    <>
      <Hero
        name={HeroSection.title}
        description={HeroSection.description}
        image={HeroSection.image}
        slug={HeroSection.title}
        breadcrumbPath={HeroSection.breadcrumbPath}
        stats={HeroSection.stats}
      />
      <PageNavigation
        activeTab={pageNavigationSection.activeTab}
        setActiveTab={setActiveTab}
        tabs={pageNavigationSection.tabs}
      />
      <ExpertReview
        pill={verdictSection.pill}
        heading={verdictSection.title}
        whatWeLike={verdictSection.whatWeLike}
        AreasOfImprovement={verdictSection.AreasOfImprovement}
        verdict={verdictSection.verdict}
      />
      <CardContainer
        heading={aboutSection.title}
        subheading={aboutSection.description}
        pill={aboutSection.pill}
        card_content={aboutSection.cards}
        grid_cols={2}
        id="about-super-top-up"
      />
      <CardContainer
        heading={benefitsSection.title}
        pill={benefitsSection.pill}
        card_content={benefitsSection.cards}
        grid_cols={4}
        id="benefits"
      />
      <Testimonial
        testimonials={testimonialSection.testimonials}
        sectionHeaderProps={testimonialSection.sectionHeaderProps}
        pill={testimonialSection.pill}
      />
      <CardContainer
        heading={highlightsSection.title}
        pill={highlightsSection.pill}
        card_content={highlightsSection.cards}
        grid_cols={4}
        id="highlights"
      />
      <CardContainer
        heading={uniqueFeaturesSection.title}
        pill={uniqueFeaturesSection.pill}
        card_content={uniqueFeaturesSection.cards}
        grid_cols={4}
        id="unique-features"
      />
      <CardContainer
        heading={eligiblityCriteriaSection.title}
        pill={eligiblityCriteriaSection.pill}
        card_content={eligiblityCriteriaSection.cards}
        grid_cols={4}
        id="eligibility"
      />
      <CardContainer
        heading={factorsToConsiderSection.title}
        pill={factorsToConsiderSection.pill}
        card_content={factorsToConsiderSection.cards}
        grid_cols={4}
        id="factors-to-consider"
      />
      <InclusionExclusionSection
        heading={inclusionSection.title}
        subheading={inclusionSection.description}
        pill={inclusionSection.pill}
        inclusions={inclusionSection.inclusions}
        exclusions={inclusionSection.exclusions}
        id="inclusions-and-exclusions"
      />
      <CategoryCards
        pill={insuranceCategorySection.pill}
        heading={insuranceCategorySection.title}
        subHeading={insuranceCategorySection.description}
        categories={insuranceCategorySection.cards}
        id="insurance-category"
      />
      <ClaimTypes
        heading={claimSettlementSection.title}
        subheading={""}
        pill={claimSettlementSection.pill}
        claimSettlements={claimSettlementSection.settlements}
      />
      {/* <ClaimTypes
        heading={renewalSection.title}
        subheading={""}
        pill={renewalSection.pill}
        claimSettlements={renewalSection.renewalSteps}
      /> */}
      <RenewalTypes
        pill={renewalSection.pill}
        heading={renewalSection.title}
        subheading={renewalSection.description}
        renewalSteps={renewalSection.renewalSteps}
      />
      <AccordianSection
        pill={howToBuySection.pill}
        heading={howToBuySection.title}
        subheading={howToBuySection.description}
        faqs={howToBuySection.steps}
        sectionTitle="Steps"
        id="how-to-buy"
      />
      {/* <FAQs
        pill={howToBuySection.pill}
        heading={howToBuySection.title}
        subheading={howToBuySection.description}
        faqs={howToBuySection.steps}
        id="how-to-buy"
      /> */}
      <LeadForm
        pill="Plan Listing"
        title="Still Confused? Get Expert Guidance"
        description="Our insurance experts can help you compare plans and find the best coverage for your needs"
      />
      <FAQs
        pill={faqSection.pill}
        heading={faqSection.title}
        subheading={faqSection.description}
        faqs={faqSection.faqs}
        id="faqs"
      />
      {/* Insurer Plans */}
      {allInsurerData && allInsurerData.length > 0 && (
        <InsurerPlan allInsurerData={allInsurerData} />
      )}
      {/* Related Blogs */}
      {blogData && blogData.blogs.length > 0 && (
        <RelatedBlogs blogData={blogData} />
      )}
    </>
  );
};

export default SuperTopUp;
