export type SuperTopUpApiResponse = {
    id: string;
    hero_title: string;
    hero_description: string;
    hero_image_url: string;
    pill_content: string;
    slug: string;
    health_insurer: {
        id: string;
        name: string;
        slug: string;
    };
    super_top_up_page_claim_settlement_section: {
        id: string;
        pill_content: string;
        section_title: string;
        section_description: string;
        super_top_up_page_claim_settlement_types: {
            id: string;
            title: string;
            type: string;
            super_top_up_page_claim_settlement_steps: {
                id: string;
                title: string;
                description: string;
            }[];
        }[];
    };
    super_top_up_page_faq_section: {
        id: string;
        pill_content: string;
        section_title: string;
        section_description: string;
        super_top_up_page_faq_section_points: {
            id: string;
            question: string;
            answer: string;
        }[];
    };
    super_top_up_page_hero_cards: {
        id: string;
        title: string;
        description: string;
        icon_url: string;
    }[];
    super_top_up_page_how_to_buy_section: {
        id: string;
        pill_content: string;
        section_title: string;
        section_description: string;
        super_top_up_page_how_to_buy_steps: {
            id: string;
            title: string;
            description: string;
        }[];
    };
    super_top_up_page_inclusion_section: {
        id: string;
        pill_content: string;
        section_title: string;
        section_description: string;
        super_top_up_page_inclusion_section_points: {
            id: string;
            title: string;
            type: string;
            description: string;
        }[];
    };
    super_top_up_page_insurance_category_section: {
        id: string;
        pill_content: string;
        section_title: string;
        section_description: string;
        super_top_up_page_insurance_category_section_cards: {
            id: string;
            title: string;
            points: string[];
            most_popular: boolean;
            icon_url: string;
        }[];
    };
    super_top_up_page_renewal_section: {
        id: string;
        pill_content: string;
        section_title: string;
        section_description: string;
        super_top_up_page_renewal_types: {
            id: string;
            type: string;
            title: string;
            super_top_up_page_renewal_steps: {
                id: string;
                title: string;
                description: string;
            }[];
        }[];
    };
    super_top_up_page_testimonial_section: {
        id: string;
        pill_content: string;
        section_title: string;
        section_description: string;
        super_top_up_page_testimonial_section_points: {
            id: string;
            name: string;
            description: string;
        }[];
    };
    super_top_up_page_verdicts: {
        id: string;
        pill_content: string;
        title: string;
        verdict: string;
        super_top_up_page_verdict_pros_cons: {
            id: string;
            title: string;
            type: string;
            points: string;
        }[];
    };
    super_top_up_page_sections: {
        id: string;
        pill_content: string;
        section: "about" | "benefits" | "highlights" | "unique_features" | "eligiblity_criteria" | "factors_to_consider";
        section_title: string;
        section_description: string;
        super_top_up_page_section_cards: {
            id: string;
            title: string;
            icon_url: string;
            description: string;
        }[];
    }[];
};
