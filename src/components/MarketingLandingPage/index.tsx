"use client";

import Hero from "./components/Hero";
import Benefits from "./components/Benefits";
import WhyUs from "./components/WhyUs";
import Testimonials from "../globals/Testimonials";
import ContactForm from "./components/ContactForm";
import { Page, Block } from "./type";
import { Suspense, useState } from "react";
import RegisterButton from "./components/RegisterBtn";
import RefundPolicy from "./components/RefundPolicy";
import { useSearchParams } from "next/navigation";

const Section = ({ data }: { data: Block }) => {
  switch (data.__component) {
    case "marketing.insurance-plans":
    case "marketing.benefits":
      return (
        <Benefits
          description={data.description}
          title={data.title}
          bgColor={data.bgColor}
        />
      );
    case "marketing.why-us":
      return <WhyUs data={data} />;
    case "marketing.testimonial":
      return <Testimonials testimonials={data.testimonial} />;
    // case "marketing.faqs":
    //   return <Faq faqs={{ id: 0, faq: data.faq }} />;
  }
};

const MarketingLandingPage = ({ data }: { data: Page }) => {
  const [showRefundPolicy, setShowRefundPolicy] = useState(false);

  return (
    <div className="">
      <Suspense>
        <Hero
          data={data.attributes.hero}
          source={data.attributes.seo.source}
          setShowRefundPolicy={setShowRefundPolicy}
        />
      </Suspense>
      {data.attributes.block.map((section, idx) => (
        <Section key={idx} data={section} />
      ))}
      <RegisterButton contactType={data.attributes.hero.contactForm.formType} />

      <RefundPolicy
        setShowRefundPolicy={setShowRefundPolicy}
        showRefundPolicy={showRefundPolicy}
        description={
          data.attributes.hero.contactForm.refundPolicy
            ? data.attributes.hero.contactForm.refundPolicy
            : ""
        }
      />
    </div>
  );
};

export default MarketingLandingPage;
