import BlogHero from "./components/blogHero";
import BlogVideo from "./components/blogVideo";
import BlogCategory from "./components/blogCategory";
import TalkToExpert from "@/components/globals/TalkToOAExpert";
import QuoteForm from "@/components/HealthInsurance/components/QuoteForm";
import { BlogPost } from "./types";

const BlogNew = ({
  data,
  category,
  title = "",
  subTitle = "",
  youtubeData,
}: {
  data: BlogPost[];
  title?: string;
  subTitle?: string;
  category: any;
  youtubeData: any;
}) => {
  return (
    <div className="max-w-[1240px] md:px-0 mx-auto">
      <BlogHero data={data} />
      <div className="pt-6 md:pt-12">
        <BlogCategory categories={category.data} />
      </div>
      <div className="px-6 md:px-0">
        <BlogVideo data={youtubeData} />
      </div>
      {/* <BlogQuote /> */}
      <div className="mt-5 md:mt-12 px-6 md:px-0 max-w-[1240px] mx-auto">
        <TalkToExpert />
      </div>
      <div className="px-6 md:px-0 mb-6 max-w-[1240px] mx-auto">
        <QuoteForm />
      </div>
    </div>
  );
};

export default BlogNew;
