"use client";

import { ArticleType } from "@/components/BlogNew/Article/types";
import Container from "@/components/globals/Container";
import dayjs from "dayjs";
import ArticleBody from "@/components/BlogNew/Article/components/ArticleBody";
import BlogCard from "@/components/BlogNew/components/blogCard";
import <PERSON>ToExpert from "@/components/globals/TalkToOAExpert";
import QuoteForm from "@/components/HealthInsurance/components/QuoteForm";
import ArticlePlans from "@/components/BlogNew/Article/components/articlePlans";
import ArticleAuthors from "@/components/BlogNew/Article/components/articleAuthors";
import Breadcrumb from "@/components/globals/Breadcrumb";
import BookACallBtn from "@/components/globals/BookACall";
import { Suspense, useEffect } from "react";
import { useSessionStorage } from "usehooks-ts";
import { usePathname, useRouter } from "next/navigation";

const Article = ({ data }: { data: ArticleType }) => {
  const healthProducts = data.attributes.healthVariants?.data;
  const termProducts = data.attributes.termVariants?.data;
  const relatedBlogs = data.attributes.relatedReads.data;

  // Create breadcrumb path
  const breadcrumbPath = [
    "home",
    "insurance",
    data.attributes.category.data.attributes.slug,
    data.attributes.slug,
  ];

  const pathname = usePathname();
  const router = useRouter();
  const [utm_source, setUtmSource] = useSessionStorage("utm_source", null);
  const [utm_medium, setUtmMedium] = useSessionStorage("utm_medium", null);
  const [utm_campaign, setUtmCampaign] = useSessionStorage(
    "utm_campaign",
    null
  );
  const [utm_content, setUtmContent] = useSessionStorage("utm_content", null);
  const [utm_term, setUtmTerm] = useSessionStorage("utm_term", null);
  useEffect(() => {
    if (typeof window !== "undefined") {
      router.replace(
        `${pathname}?${utm_source !== null ? `utm_source=${utm_source}` : ""}${
          utm_medium !== null ? `&utm_medium=${utm_medium}` : ""
        }${utm_campaign !== null ? `&utm_campaign=${utm_campaign}` : ""}${
          utm_content !== null ? `&utm_content=${utm_content}` : ""
        }${utm_term !== null ? `&utm_term=${utm_term}` : ""}`
      );
    }
  }, []);

  return (
    <Container>
      <div className="font-manrope">
        <div className="my-5">
          {/* Breadcrumb */}
          <Suspense>
            <Breadcrumb path={breadcrumbPath} />
          </Suspense>

          {/* category tag */}
          <div className="flex items-center md:my-5 my-2">
            <div className="bg-secondary-blue-2 text-white px-2 py-1 text-sm rounded-[4px] font-medium">
              {data.attributes.category.data.attributes.name.toUpperCase()}
            </div>
          </div>

          {/* title */}
          <h1 className="md:text-4xl text-2xl font-bold mb-1 text-ntrl-black-1">
            {data.attributes.Title}
          </h1>

          {/* subtitle */}
          <p className="md:text-base text-sm mb-2 text-ntrl-black-1">
            {data.attributes.subtitle}
          </p>

          {/* duration,author,date */}
          <div className="flex flex-col md:flex-row items-center md:gap-6 gap-4 mt-1">
            <BookACallBtn className="mt-2 md:mt-0 bg-secondary-blue-2 hover:bg-secondary-blue-3 rounded-full shadow-md px-6 py-2 md:px-6 md:py-2 text-white md:text-lg" />
            <div className="flex items-center">
              <p className="text-sm text-ntrl-black-1 border-r border-ntrl-black-1 pr-2">
                {data.attributes.readingtime}
              </p>
              <p className="text-sm text-ntrl-black-1 px-2 border-r border-ntrl-black-1">
                {data.attributes.author.data.attributes.name}
              </p>
              <p className="text-sm text-ntrl-black-1 pl-2">
                {dayjs(data.attributes.createdAt).format("MMMM DD, YYYY")}
              </p>
            </div>
          </div>
          {/* image */}
          <div className="mt-6">
            <img
              src={data.attributes.Thumbnail.data.attributes.url}
              alt={data.attributes.Title}
              className="w-full md:h-[500px] object-cover md:object-scale-down rounded-2xl"
            />
          </div>
          {/* content */}
          <div className="mt-6">
            <ArticleBody desc={data.attributes.Description} />
          </div>
        </div>
        {data.attributes.authorNote && (
          <div className="mt-6">
            <ArticleAuthors />
          </div>
        )}
        {/* <ArticleNewsLetter /> */}
      </div>
      <TalkToExpert />
      {/* Related Articles */}
      <div className="mt-6">
        <h2
          className={`text-2xl md:text-3xl text-center font-medium mb-4 text-ntrl-black-1 ${
            relatedBlogs.length > 0 ? "" : "hidden"
          }`}
        >
          Related Articles
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-10">
          {relatedBlogs.length > 0 &&
            relatedBlogs.map((blog) => (
              <BlogCard
                key={blog.id}
                imageUrl={blog.attributes.Thumbnail.data.attributes.url}
                tag={blog.attributes.category.data.attributes.name}
                title={blog.attributes.Title}
                date={blog.attributes.createdAt}
                author={blog.attributes.author.data.attributes.name}
                description={blog.attributes.subtitle}
                url={`/insurance/${blog.attributes.category.data.attributes.slug}/${blog.attributes.slug}`}
              />
            ))}
        </div>
      </div>
      {(healthProducts?.length > 0 || termProducts?.length > 0) && (
        <>
          <h2 className="md:text-3xl text-2xl font-medium mb-6 text-center">
            Related Products
          </h2>
          <ArticlePlans plans={healthProducts} />
          <ArticlePlans plans={termProducts} />
        </>
      )}
      {/* Get a quote */}
      {data.attributes.category.data.attributes.name
        .toLowerCase()
        .includes("health") && (
        <div className="w-full my-6">
          <QuoteForm />
        </div>
      )}
    </Container>
  );
};

export default Article;
