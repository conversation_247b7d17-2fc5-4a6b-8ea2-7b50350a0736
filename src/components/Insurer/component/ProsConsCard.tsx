import { BodyLarge, HeadingMedium } from "@/components/UI/Typography";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";
import {
  IoMdCheckmarkCircleOutline,
  IoMdCloseCircleOutline,
} from "react-icons/io";
import parse, { DOMNode, domToReact, Element } from "html-react-parser";

//custom html-renderer for this component
const htmlRenderer = (domNode: DOMNode) => {
  if (domNode.type === "tag" && domNode.attribs) {
    let attrs = domNode.attribs;
    if (attrs.style) {
      attrs.style = "";
    }

    switch (domNode.name) {
      case "p":
        // Check if p contains a span and extract its content
        if (domNode.children && domNode.children.length > 0) {
          const spanChild = domNode.children.find(
            (child): child is Element =>
              "name" in child && child.name === "span"
          );

          if (spanChild) {
            // Create a new p element with the span's content
            return (
              <BodyLarge as="p" className="text-neutral-700">
                {domToReact(spanChild.children as DOMNode[])}
              </BodyLarge>
            );
          }
        }
        return (
          <BodyLarge as="p" className="text-neutral-700">
            {domToReact(domNode.children as DOMNode[])}
          </BodyLarge>
        );
    }
    return domNode;
  }
};

export type ProsConsItem = {
  id: string;
  title: string;
  description: string;
  type: "pro" | "con";
};

export type ProsConsData = {
  pros: ProsConsItem[];
  cons: ProsConsItem[];
};

const ProsConsCard: React.FC<{
  data?: ProsConsData;
}> = ({ data }) => {
  // Add safety checks for undefined data
  if (!data) {
    const renderEmptyCard = (title: string) => (
      <div className="flex-1 bg-white rounded-xl p-4 md:px-6 md:py-5 gap-4 shadow-sm flex flex-col items-start justify-start border-[1px] border-primary-200 transition-all">
        <HeadingMedium weight="semibold" className="text-neutral-1100">
          {title}
        </HeadingMedium>
        <BodyLarge className="text-neutral-800">Not available</BodyLarge>
      </div>
    );

    return (
      <>
        {/* Desktop Layout */}
        <div className="hidden md:flex flex-col md:flex-row gap-4 w-full">
          {renderEmptyCard("Pros")}
          {renderEmptyCard("Cons")}
        </div>

        {/* Mobile Carousel Layout */}
        <div className="block md:hidden w-full">
          <MobileCarousel totalSlides={2}>
            <MobileCarouselItem>{renderEmptyCard("Pros")}</MobileCarouselItem>
            <MobileCarouselItem>{renderEmptyCard("Cons")}</MobileCarouselItem>
          </MobileCarousel>
        </div>
      </>
    );
  }

  const { pros = [], cons = [] } = data;

  const renderCard = (
    title: string,
    items: ProsConsItem[],
    icon: React.ReactNode
  ) => (
    <div className="flex-1 bg-white rounded-xl p-4 md:px-6 md:py-5 gap-4 shadow-sm flex flex-col items-start justify-start border-[1px] border-primary-200 transition-all">
      <div className="w-full rounded-t-xl">
        <HeadingMedium weight="semibold" className="text-neutral-1100">
          {title}
        </HeadingMedium>
      </div>
      {items.length === 0 ? (
        <BodyLarge className="text-neutral-800">Not available</BodyLarge>
      ) : (
        <ul className="flex flex-col items-start gap-3">
          {items.map((item: ProsConsItem) => (
            <li key={item.id} className="flex items-start gap-3">
              <div className="flex-shrink-0 w-5 h-5 flex items-center justify-center mt-1">
                {icon}
              </div>
              {parse(item.description, { replace: htmlRenderer })}
            </li>
          ))}
        </ul>
      )}
    </div>
  );

  return (
    <>
      {/* Desktop Layout */}
      <div className="hidden md:flex flex-col md:flex-row gap-4 w-full">
        {renderCard(
          "Pros",
          pros,
          <IoMdCheckmarkCircleOutline className="text-secondary-400 w-5 h-5" />
        )}
        {renderCard(
          "Cons",
          cons,
          <IoMdCloseCircleOutline className="text-red-400 w-5 h-5" />
        )}
      </div>

      {/* Mobile Carousel Layout */}
      <div className="block md:hidden w-full">
        <MobileCarousel totalSlides={2}>
          <MobileCarouselItem>
            {renderCard(
              "Pros",
              pros,
              <IoMdCheckmarkCircleOutline className="text-secondary-400 w-5 h-5" />
            )}
          </MobileCarouselItem>
          <MobileCarouselItem>
            {renderCard(
              "Cons",
              cons,
              <IoMdCloseCircleOutline className="text-red-400 w-5 h-5" />
            )}
          </MobileCarouselItem>
        </MobileCarousel>
      </div>
    </>
  );
};

export default ProsConsCard;
