import { Button } from "@/components/UI/Button";
import { BodyLarge, HeadingMedium } from "@/components/UI/Typography";
import parse, { DOMNode, domToReact, Element } from "html-react-parser";
import { OpenCalendlyPopup } from "@/utils/calendlyFunction";
import { FaShieldAlt } from "react-icons/fa";

//custom html-renderer for this component
const htmlRenderer = (domNode: DOMNode) => {
  if (domNode.type === "tag" && domNode.attribs) {
    let attrs = domNode.attribs;
    if (attrs.style) {
      attrs.style = "";
    }

    switch (domNode.name) {
      case "p":
        // Check if p contains a span and extract its content
        if (domNode.children && domNode.children.length > 0) {
          const spanChild = domNode.children.find(
            (child): child is Element =>
              "name" in child && child.name === "span"
          );

          if (spanChild) {
            // Create a new p element with the span's content
            return (
              <BodyLarge as="p" className="text-neutral-1100">
                {domToReact(spanChild.children as DOMNode[])}
              </BodyLarge>
            );
          }
        }
        return (
          <BodyLarge as="p" className="text-neutral-1100">
            {domToReact(domNode.children as DOMNode[])}
          </BodyLarge>
        );
    }
    return domNode;
  }
};

type InsurerType = {
  id: string;
  title: string;
  description: string;
  button_text: string;
};

const InsurerTypeCard = ({ type }: { type: InsurerType }) => {
  function handleClick() {
    OpenCalendlyPopup();
  }

  return (
    <div className="bg-white rounded-xl p-6 shadow-sm border border-primary-200 flex flex-col items-center gap-4 text-center w-full h-full">
      <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
        <FaShieldAlt className="w-6 h-6 text-tertiary-orange-400" />
      </div>
      <HeadingMedium as="h3" className="font-semibold text-neutral-1100">
        {type.title}
      </HeadingMedium>
      <div className="flex-1 text-neutral-1100">
        {parse(type.description, { replace: htmlRenderer })}
      </div>
      <Button variant="primary" className="mt-2 w-full" onClick={handleClick}>
        <span className="text-white text-sm">{type.button_text}</span>
      </Button>
    </div>
  );
};

export default InsurerTypeCard;
