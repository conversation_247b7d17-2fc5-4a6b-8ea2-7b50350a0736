import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import SectionContainerSmall from "@/components/globals/SectionContainerSmall";
import PillBadge from "@/components/globals/PillBadge";
import { HeadingXLarge, BodyLarge } from "@/components/UI/Typography";
import ExpertReviewLists from "@/components/Insurer/component/ExpertReviewLists";
import Verdict from "@/components/Insurer/component/Verdict";
import parse, { DOMNode, domToReact, Element } from "html-react-parser";

//custom html-renderer for this component
const htmlRenderer = (domNode: DOMNode) => {
  if (domNode.type === "tag" && domNode.attribs) {
    let attrs = domNode.attribs;
    if (attrs.style) {
      attrs.style = "";
    }

    switch (domNode.name) {
      case "p":
        // Check if p contains a span and extract its content
        if (domNode.children && domNode.children.length > 0) {
          const spanChild = domNode.children.find(
            (child): child is Element =>
              "name" in child && child.name === "span"
          );

          if (spanChild) {
            // Create a new p element with the span's content
            return (
              <BodyLarge as="p" className="text-neutral-1100 text-center">
                {domToReact(spanChild.children as DOMNode[])}
              </BodyLarge>
            );
          }
        }
        return (
          <BodyLarge as="p" className="text-neutral-1100 text-center">
            {domToReact(domNode.children as DOMNode[])}
          </BodyLarge>
        );
    }
    return domNode;
  }
};

const ExpertReview = ({
  pill,
  heading,
  subheading,
  whatWeLike,
  AreasOfImprovement,
  verdict,
}: {
  pill: string;
  heading: string;
  subheading?: string ;
  whatWeLike: {
    heading: string;
    points: string[];
  };
  AreasOfImprovement: {
    heading: string;
    points: string[];
  };
  verdict: string;
}) => {
  return (
    <SectionContainerLarge id="expert-review" className="!p-0">
      {/* Custom Section Header */}
      <div className="flex flex-col items-center gap-2 md:gap-3 mx-auto mb-6 px-6 md:px-0">
        <PillBadge pill={pill} />
        <HeadingXLarge className="text-center text-neutral-1100">
          {heading}
        </HeadingXLarge>
        {parse(subheading || "", {
          replace: htmlRenderer,
        })}
      </div>

      <SectionContainerSmall className="!mb-4 md:!mb-6 !p-0">
        <ExpertReviewLists
          whatWeLike={whatWeLike}
          areasOfImprovement={AreasOfImprovement}
        />
      </SectionContainerSmall>
       <Verdict verdict={verdict} heading="Final Verdict" />
    </SectionContainerLarge>
  );
};

export default ExpertReview;
