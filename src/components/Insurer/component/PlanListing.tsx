import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import SectionHeader from "@/components/globals/DSComponentsV0/SectionHeaderWithParse";
import { Grid } from "@/components/UI/Grid";
import PlanCard from "@/components/globals/DSComponentsV0/PlanCard";
import { HeadingLarge, HeadingSmall } from "@/components/UI/Typography";

type planListingSection = {
  id: string;
  insurerName: string;
  insurerSlug: string;
  logoUrl: string;
  plan_listing: {
    id: string;
    title: string;
    cards: {
      id: string;
      healthVariantId: string;
      healthProductVariant: {
        id: string;
        variant_name: string;
        variant_slug: string;
        temp_slug?: string | undefined;
      };
    }[];
  }[];
};

const PlanListing = ({
  planListingSection,
}: {
  planListingSection: planListingSection;
}) => {
  planListingSection.insurerName = planListingSection.insurerName.endsWith(
    "Health Insurance"
  )
    ? planListingSection.insurerName.replace("Health Insurance", "")
    : planListingSection.insurerName;
  return (
    <SectionContainerLarge className="">
      <SectionHeader
        pill={planListingSection.insurerName + "Health Insurance Plans"}
        heading={planListingSection.insurerName + "Health Insurance Plans"}
        subheading=""
        component="h2"
        className="!mb-12 !px-0"
      />

      {planListingSection.plan_listing.map((planListing) => (
        <div key={planListing.id}>
          <HeadingLarge className="text-center mb-6 text-neutral-1000" as="h3">
            {planListing.title}
          </HeadingLarge>

          <Grid cols={3} gap={6} mobileCols={1} gapY={4}>
            {planListing.cards.map((card) => (
              <PlanCard
                key={card.id}
                plan={{
                  logo_url: planListingSection.logoUrl,
                  plan_title: planListingSection.insurerName + " " + card.healthProductVariant.variant_name,
                  redirect_url: `${process.env.NEXT_PUBLIC_BASE_URL}/health-insurance/${planListingSection.insurerSlug}/${card.healthProductVariant.temp_slug}`,
                }}
              />
            ))}
          </Grid>
        </div>
      ))}
    </SectionContainerLarge>
  );
};

export default PlanListing;
