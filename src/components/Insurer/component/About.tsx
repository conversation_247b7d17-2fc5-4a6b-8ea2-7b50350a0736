import PillBadge from "@/components/globals/PillBadge";
import { BodyLarge, HeadingXLarge } from "@/components/UI/Typography";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import parse, { DOMNode, domToReact, Element } from "html-react-parser";
import { useState } from "react";

//custom html-renderer for this component
const htmlRenderer = (domNode: DOMNode) => {
  if (domNode.type === "tag" && domNode.attribs) {
    let attrs = domNode.attribs;
    if (attrs.style) {
      attrs.style = "";
    }

    switch (domNode.name) {
      case "p":
        // Check if p contains a span and extract its content
        if (domNode.children && domNode.children.length > 0) {
          const spanChild = domNode.children.find(
            (child): child is Element =>
              "name" in child && child.name === "span"
          );

          if (spanChild) {
            // Create a new p element with the span's content
            return (
              <BodyLarge as="p" className="text-neutral-1100 text-justify">
                {domToReact(spanChild.children as DOMNode[])}
              </BodyLarge>
            );
          }
        }
        return (
          <BodyLarge as="p" className="text-neutral-1100 text-justify">
            {domToReact(domNode.children as DOMNode[])}
          </BodyLarge>
        );
    }
    return domNode;
  }
};

// Shared function to extract text content from HTML for word counting
const extractTextFromHTML = (htmlString: string): string => {
  // Check if we're in the browser environment
  if (typeof window === "undefined") {
    // Server-side fallback: simple regex to strip HTML tags
    return htmlString
      .replace(/<[^>]*>/g, " ")
      .replace(/\s+/g, " ")
      .trim();
  }

  const temp = document.createElement("div");
  temp.innerHTML = htmlString;
  return temp.textContent || temp.innerText || "";
};

// Mobile component
const MobileAbout = ({
  pill,
  about,
  heading,
}: {
  pill: string;
  about: string;
  heading: string;
}) => {
  const [showFullText, setShowFullText] = useState(false);

  const textContent = extractTextFromHTML(about);
  const words = textContent.split(/\s+/).filter((word) => word.length > 0);
  const shouldShowSeeMore = words.length > 35;

  const truncatedText =
    shouldShowSeeMore && !showFullText
      ? words.slice(0, 35).join(" ") + "..."
      : about;

  const parsedAbout = parse(
    shouldShowSeeMore && !showFullText ? truncatedText : about,
    {
      replace: htmlRenderer,
    }
  );

  return (
    <SectionContainerLarge>
      <div className="p-4 border border-primary-300 rounded-xl">
        {parsedAbout}
        {shouldShowSeeMore && (
          <button
            onClick={() => setShowFullText(!showFullText)}
            className="text-primary-800 text-base font-semibold hover:underline focus:outline-none mt-2"
          >
            {showFullText ? "See less" : "See more"}
          </button>
        )}
      </div>
    </SectionContainerLarge>
  );
};

// Desktop component with full content
const DesktopAbout = ({
  pill,
  about,
  heading,
}: {
  pill: string;
  about: string;
  heading: string;
}) => {
  const [showFullText, setShowFullText] = useState(false);

  const textContent = extractTextFromHTML(about);
  const words = textContent.split(/\s+/).filter((word) => word.length > 0);
  const shouldShowSeeMore = words.length > 50; // Higher threshold for desktop

  const truncatedText =
    shouldShowSeeMore && !showFullText
      ? words.slice(0, 50).join(" ") + "..."
      : about;

  const parsedAbout = parse(
    shouldShowSeeMore && !showFullText ? truncatedText : about,
    {
      replace: htmlRenderer,
    }
  );

  return (
    <SectionContainerLarge>
      <div className="p-4 md:bg-primary-100 border border-primary-300 rounded-xl">
        {parsedAbout}
        {shouldShowSeeMore && (
          <button
            onClick={() => setShowFullText(!showFullText)}
            className="text-primary-800 text-base font-semibold hover:underline focus:outline-none mt-2"
          >
            {showFullText ? "See less" : "See more"}
          </button>
        )}
      </div>
    </SectionContainerLarge>
  );
};

// Main component with responsive rendering
const About = ({
  about,
  heading,
  pill,
}: {
  about: string;
  heading: string;
  pill: string;
}) => {
  return (
    <>
      <SectionContainerLarge id="about" className="!mb-0">
        <PillBadge pill={pill} />
        <HeadingXLarge
          as="h2"
          className="text-neutral-1100 mb-4 md:mb-6 text-center font-semibold mt-2 md:mt-3"
        >
          {heading}
        </HeadingXLarge>
      </SectionContainerLarge>
      {/* Desktop Layout */}
      <div className="hidden md:block">
        <DesktopAbout about={about} heading={heading} pill={pill} />
      </div>

      {/* Mobile Layout */}
      <div className="md:hidden">
        <MobileAbout about={about} heading={heading} pill={pill} />
      </div>
    </>
  );
};

export default About;
