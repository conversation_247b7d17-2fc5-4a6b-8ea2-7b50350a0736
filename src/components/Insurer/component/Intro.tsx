import Image from "next/image";
import { HeadingXLarge, BodyLarge } from "@/components/UI/Typography";
import parse, { DOMNode, domToReact, Element } from "html-react-parser";
import { htmlParser } from "@/utils/htmlParser";

//custom html-renderer for this component
const htmlRenderer = (domNode: DOMNode) => {
  if (domNode.type === "tag" && domNode.attribs) {
    let attrs = domNode.attribs;
    if (attrs.style) {
      attrs.style = "";
    }

    switch (domNode.name) {
      case "p":
        // Check if p contains a span and extract its content
        if (domNode.children && domNode.children.length > 0) {
          const spanChild = domNode.children.find(
            (child): child is Element =>
              "name" in child && child.name === "span"
          );

          if (spanChild) {
            // Create a new p element with the span's content
            return (
              <BodyLarge as="p" className="text-neutral-1100 text-center">
                {domToReact(spanChild.children as DOMNode[])}
              </BodyLarge>
            );
          }
        }
        return (
          <BodyLarge as="p" className="text-neutral-1100 text-center">
            {domToReact(domNode.children as DOMNode[])}
          </BodyLarge>
        );
    }
    return domNode;
  }
};

const Intro = ({
  name,
  description,
  image,
}: {
  name: string;
  description: string;
  image: string;
}) => {
  return (
    <div className="flex flex-col gap-4 items-center text-center">
      <div className="w-full h-full flex justify-center items-center">
        <Image
          src={image}
          alt="Intro"
          width={120}
          height={100}
          objectFit="contain"
        />
      </div>
      <HeadingXLarge
        as="h1"
        weight="bold"
        className="text-neutral-1100 text-center"
      >
        {name}
      </HeadingXLarge>
      {htmlParser(description, {
        components: {
          p: BodyLarge,
        },
        classNames: {
          p: "text-neutral-1100 text-center",
        },
      })}
    </div>
  );
};

export default Intro;
