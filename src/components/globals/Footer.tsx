"use client";

import CustomLink from "./CustomLink";
import Container from "./Container";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/navigation";
import { BodySmall, HeadingSmall } from "../UI/Typography";

const Footer = () => {
  const search = useSearchParams();
  const utm = search.get("utm_source");
  const router = useRouter();
  return (
    <footer
      id="footer"
      className=" bg-neutral-100 px-10 py-12 md:px-0 md:py-16 font-poppins"
    >
      <Container className="!px-0">
        <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-6 md:gap-8">
          {/* LOGOS */}
          <div className=" md:w-1/4">
            <div className="flex items-center cursor-pointer group">
              <div className="w-[141.19px] h-8 relative">
                <Image
                  src="https://cdn.oasr.in/oa-site/cms-uploads/media/Logo_File_7th_August_4ea73a2feb.svg"
                  alt="Oneassure"
                  fill
                  style={{ objectFit: "contain" }}
                />
              </div>
            </div>
            <BodySmall className="text-neutral-1100 mb-3">
              OneAssure is a full-stack digital Insurance Platform
            </BodySmall>
            <div className="flex mb-4">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL}/irdai.svg`}
                width={64}
                height={64}
                alt="irdai"
                className=""
                style={{ objectFit: "contain" }}
              />
              <Image
                src={
                  "https://cdn.oasr.in/oa-site/cms-uploads/media/ISO_27001_9d22b24184.png"
                }
                width={75}
                height={75}
                alt="irdai"
                className="mx-8"
                style={{ objectFit: "contain" }}
              />
              <Image
                src={
                  "https://cdn.oasr.in/oa-site/cms-uploads/media/SOC_2_facf1e938d.png"
                }
                width={75}
                height={75}
                alt="irdai"
                className=""
                style={{ objectFit: "contain" }}
              />
            </div>
            <div className="flex">
              <CustomLink href={"https://x.com/OneAssure?s=20"} utm={utm}>
                <Image
                  src="https://d1wamg5uas0pit.cloudfront.net/Global_solid_twitter.svg-1698751099"
                  width={24}
                  height={24}
                  alt="twitter"
                  className="mr-8 cursor-pointer"
                />
              </CustomLink>

              <CustomLink
                href={"https://www.facebook.com/oneassure/"}
                utm={utm}
              >
                <Image
                  src="https://d1wamg5uas0pit.cloudfront.net/Global_solid_facebook.svg-1698751122"
                  width={24}
                  height={24}
                  alt="facebook"
                  className="mr-8 cursor-pointer"
                />
              </CustomLink>

              <CustomLink
                href={"https://in.linkedin.com/company/oneassure"}
                utm={utm}
              >
                <Image
                  src="https://d1wamg5uas0pit.cloudfront.net/Global_solid_linked.svg-1698751149"
                  width={24}
                  height={24}
                  alt="linkedin"
                  className="mr-8 cursor-pointer"
                />
              </CustomLink>

              <CustomLink
                href={"https://www.instagram.com/oneassureindia/"}
                utm={utm}
              >
                <Image
                  src="https://d1wamg5uas0pit.cloudfront.net/Gloab_solid_instagram.svg-1698751177"
                  width={24}
                  height={24}
                  alt="instagram"
                  className="mr-8 cursor-pointer"
                />
              </CustomLink>

              <CustomLink
                href={"https://youtube.com/@oneassure8897?si=0iVX41Bhs_cR-AIS"}
                utm={utm}
              >
                <Image
                  src="https://d1wamg5uas0pit.cloudfront.net/Global_solid_youtube.svg-1698751200"
                  width={24}
                  height={24}
                  alt="youtube"
                  className="cursor-pointer"
                />
              </CustomLink>
            </div>
          </div>

          {/* Navigation Links */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 md:w-3/4">
            <div className="">
              <HeadingSmall weight="bold" className="mb-3 text-neutral-1100">
                Insurances
              </HeadingSmall>
              <CustomLink href="/term-insurance" utm={utm}>
                <BodySmall className="mb-1 text-neutral-1100">
                  Term Insurance
                </BodySmall>
              </CustomLink>
              <CustomLink href="/health-insurance" utm={utm}>
                <BodySmall className="mb-1 text-neutral-1100">
                  Health Insurance
                </BodySmall>
              </CustomLink>
              <CustomLink href="/compare-health-insurance-plans" utm={utm}>
                <BodySmall className="mb-1 text-neutral-1100">
                  Compare Health Insurance Plans
                </BodySmall>
              </CustomLink>
              <CustomLink href="/html-sitemaps/compare-health-insurance" utm={utm}>
                <BodySmall className="mb-1 text-neutral-1100">
                  Explore Health Insurance Comparison
                </BodySmall>
              </CustomLink>
              
              <div className="md:flex hidden flex-col gap-2 border-t border-neutral-1100 mt-3 pt-3 w-fit">
                <HeadingSmall weight="bold" className="text-neutral-1100">
                  Tools
                </HeadingSmall>
                <CustomLink href="/bmi-calculator-men-women" utm={utm}>
                  <BodySmall className="text-neutral-1100">
                    BMI Calculator
                  </BodySmall>
                </CustomLink>
              </div>
            </div>
            <div className="md:hidden">
              <HeadingSmall weight="bold" className="mb-3 text-neutral-1100">
                Tools
              </HeadingSmall>
              <CustomLink href="/bmi-calculator-men-women" utm={utm}>
                <BodySmall className="text-neutral-1100">
                  BMI Calculator
                </BodySmall>
              </CustomLink>
            </div>
            <div className="">
              <HeadingSmall weight="bold" className="mb-3 text-neutral-1100">
                Company
              </HeadingSmall>
              <CustomLink href="/about" utm={utm}>
                <BodySmall className="mb-1 text-neutral-1100">
                  About Us
                </BodySmall>
              </CustomLink>
              <CustomLink href={"/contact-us"} utm={utm}>
                <BodySmall className="mb-1 text-neutral-1100">
                  Contact Us
                </BodySmall>
              </CustomLink>
              <CustomLink href="/careers" utm={utm}>
                <BodySmall className="mb-1 text-neutral-1100">
                  Careers
                </BodySmall>
              </CustomLink>
              <CustomLink href="/insurance" utm={utm}>
                <BodySmall className="mb-1 text-neutral-1100">Blogs</BodySmall>
              </CustomLink>
              <CustomLink href="/claims" utm={utm}>
                <BodySmall className="mb-1 text-neutral-1100">Claims</BodySmall>
              </CustomLink>
            </div>
            <div className="">
              <HeadingSmall weight="bold" className="mb-3 text-neutral-1100">
                Policy
              </HeadingSmall>
              <span
                className="text-sm mb-1 display-block"
                onClick={() => {
                  router.push("/documents/privacy-policy");
                }}
              >
                <BodySmall className="mb-1 cursor-pointer text-neutral-1100">
                  Privacy Policy
                </BodySmall>
              </span>

              <span
                className="text-sm mb-1 display-block"
                onClick={() => {
                  router.push("/documents/cancellation-and-refund");
                }}
              >
                <BodySmall className="mb-1 cursor-pointer text-neutral-1100">
                  Payments Terms
                </BodySmall>
              </span>

              <span
                className="text-sm mb-1 display-block"
                onClick={() => {
                  router.push("/documents/terms-and-conditions");
                }}
              >
                <BodySmall className="mb-1 cursor-pointer text-neutral-1100">
                  Terms & Conditions
                </BodySmall>
              </span>

              <span
                className="text-sm mb-1 display-block"
                onClick={() => {
                  router.push("/documents/license-information");
                }}
              >
                <BodySmall className="mb-1 cursor-pointer text-neutral-1100">
                  License Information
                </BodySmall>
              </span>

              <span
                className="text-sm mb-1 display-block"
                onClick={() => {
                  router.push("/documents/code-of-conduct");
                }}
              >
                <BodySmall className="mb-1 cursor-pointer text-neutral-1100">
                  Code of Conduct
                </BodySmall>
              </span>

              <span
                className="text-sm mb-1 display-block"
                onClick={() => {
                  router.push("/documents/grievance-redressal");
                }}
              >
                <BodySmall className="mb-1 cursor-pointer text-neutral-1100">
                  Grievance Redressal
                </BodySmall>
              </span>
            </div>
            {/* Contact Information */}
            <div className="mb-6 md:mb-8">
              <HeadingSmall weight="bold" className="mb-3 text-neutral-1100">
                Contact Us
              </HeadingSmall>
              <BodySmall className="text-neutral-1100">
                Prost Technologies Private Limited
              </BodySmall>
              <BodySmall className="text-neutral-1100">
                CIN- U74999KA2019PTC128430
              </BodySmall>
              <BodySmall className="text-neutral-1100">
                Address - 1st Floor, Gopala Krishna Complex, Residency Road,
                Bengaluru, Karnataka, India - 560025
              </BodySmall>
              <div className="mt-3">
                <span className="flex gap-1">
                  <BodySmall weight="bold" className="text-neutral-1100">
                    Phone -
                  </BodySmall>
                  <BodySmall className="text-neutral-1100">
                    ​+91 6364334343
                  </BodySmall>
                </span>
                <span className="flex gap-1">
                  <BodySmall weight="bold" className="text-neutral-1100">
                    Mail -
                  </BodySmall>
                  <BodySmall className="text-neutral-1100">
                    <EMAIL>
                  </BodySmall>
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Divider */}
        <div className="w-full border border-primary-300 mb-6 md:mb-8"></div>

        <BodySmall className="text-neutral-1100 text-center">
          ​Prost Insurance Brokers Pvt. Ltd.(OneAssure), 1st floor,
          91springboard, MG Road, Gopala Krishna Complex 45/3, Residency Road,
          Mahatma Gandhi Rd, Bengaluru, Karnataka 560025.License No. 756, Direct
          Broker (Life & General), Valid from: 22/07/2024 to 21/07/2027
        </BodySmall>
      </Container>
    </footer>
  );
};

export default Footer;
