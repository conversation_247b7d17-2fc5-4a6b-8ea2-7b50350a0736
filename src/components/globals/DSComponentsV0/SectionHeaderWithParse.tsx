import { BodyLarge, HeadingXLarge } from "@/components/UI/Typography";
import { useRef, useState, useEffect } from "react";
import parse, { DOMNode, domToReact, Element } from "html-react-parser";
import { convertToPlain } from "@/utils/rtfToPlain";
import PillBadge from "../PillBadge";

const htmlRenderer = (domNode: DOMNode) => {
  if (domNode.type === "tag" && domNode.attribs) {
    let attrs = domNode.attribs;
    if (attrs.style) {
      attrs.style = "";
    }

    switch (domNode.name) {
      case "p":
        // Check if p contains a span and extract its content
        if (domNode.children && domNode.children.length > 0) {
          const spanChild = domNode.children.find(
            (child): child is Element =>
              "name" in child && child.name === "span"
          );

          if (spanChild) {
            // Create a new p element with the span's content
            return (
              <BodyLarge as="p" className="text-neutral-1100">
                {domToReact(spanChild.children as DOMNode[])}
              </BodyLarge>
            );
          }
        }
        return (
          <BodyLarge as="p" className="text-neutral-1100">
            {domToReact(domNode.children as DOMNode[])}
          </BodyLarge>
        );
    }
    return domNode;
  }
};

const SectionHeader = ({ pill, heading, subheading, component, pillComponent, className }: {
  pill?: string;
  heading: string;
  subheading?: string;
  component?: "h1" | "h2";
  pillComponent?: "h2" | "h3" | "p";
  className?: string;
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [needsReadMore, setNeedsReadMore] = useState(false);
  const textRef = useRef<HTMLDivElement>(null);

  // Detect overflow once after mount
  useEffect(() => {
    if (textRef.current) {
      const el = textRef.current;
      if (el.scrollHeight > el.clientHeight) setNeedsReadMore(true);
    }
  }, [subheading]);

  return (
    <div className={`flex flex-col items-center gap-2 md:gap-3 mx-auto mb-6 px-6 ${className}`}>
      {pill && <PillBadge pill={pill} as={pillComponent} />}
        <HeadingXLarge className="text-center text-neutral-1100" as={component}>
          {heading}
        </HeadingXLarge>
      
      { subheading && convertToPlain(subheading).trim() && (
        <div className="text-center">
          {/* Desktop: full text */}
          <div className="text-neutral-1100 hidden md:block">
            {parse(subheading, { replace: htmlRenderer })}
          </div>

          {/* Mobile: clamp until expanded */}
          <div
            ref={textRef}
            className={`text-neutral-1100 md:hidden
              ${!isExpanded ? "line-clamp-2" : ""}`}
          >
            {parse(subheading, { replace: htmlRenderer })}
          </div>

          {needsReadMore && (
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-primary-800 hover:text-primary-600 text-sm font-medium mt-1"
            >
              {isExpanded ? "Read less" : "Read more"}
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default SectionHeader;
