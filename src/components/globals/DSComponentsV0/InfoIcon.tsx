import { html<PERSON>enderer } from "@/utils/htmlRenderer";
import parse from "html-react-parser";
import React, { useState, useRef, useEffect } from "react";
import { HiInformationCircle } from "react-icons/hi2";

interface InfoIconProps {
  hintText?: string;
  className?: string;
}

const InfoIcon: React.FC<InfoIconProps> = ({ hintText, className = "" }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isTapped, setIsTapped] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });
  const iconRef = useRef<HTMLDivElement>(null);

  const isTooltipVisible = isHovered || isTapped;

  useEffect(() => {
    if (isTooltipVisible && iconRef.current) {
      const rect = iconRef.current.getBoundingClientRect();
      setTooltipPosition({
        top: rect.top - 10, // Position above the icon
        left: rect.left + rect.width / 2, // Center horizontally
      });
    }
  }, [isTooltipVisible]);

  // Close tooltip when clicking outside on mobile
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isTapped && iconRef.current && !iconRef.current.contains(event.target as Node)) {
        setIsTapped(false);
      }
    };

    if (isTapped) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isTapped]);

  if (!hintText) {
    return null;
  }

  const tooltip = isTooltipVisible && (
    <div
      className="fixed px-3 py-2 bg-neutral-900 text-white text-sm rounded-lg shadow-lg z-[9999] max-w-xs min-w-[200px] pointer-events-none"
      style={{
        top: `${tooltipPosition.top}px`,
        left: `${tooltipPosition.left}px`,
        transform: 'translateX(-50%) translateY(-100%)',
      }}
    >
      <div className="whitespace-normal break-words text-center text-sm">
        {parse(hintText, { replace: htmlRenderer })}
      </div>
      {/* Arrow */}
      <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-neutral-900"></div>
    </div>
  );

  return (
    <>
      <div
        ref={iconRef}
        className={`inline-flex items-center justify-center transition-colors duration-200 ${className}`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          setIsTapped(!isTapped);
        }}
        onTouchStart={(e) => {
          e.preventDefault();
          e.stopPropagation();
          setIsTapped(!isTapped);
        }}
      >
        <HiInformationCircle className="w-5 h-5 text-neutral-500 hover:text-neutral-700" />
      </div>

      {tooltip}
    </>
  );
};

export default InfoIcon;
