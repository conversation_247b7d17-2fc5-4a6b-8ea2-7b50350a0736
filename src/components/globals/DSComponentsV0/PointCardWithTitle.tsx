import { BodyLarge, HeadingMedium, HeadingSmall } from "@/components/UI/Typography";

export default function PointCardWithTitle({
  title,
  points,
  pointIcon,
  className,
  as,
}: {
  title?: string;
  points?: {
    title: string;
    description: string;
  }[];
  pointIcon?: React.ReactNode;
  className?: string;
  as?: "h2" | "h3" | "h4" | "h5" | "h6";
}) {
  return (
    <div className={`bg-white rounded-xl p-6 shadow-sm border border-primary-200 flex flex-col items-center gap-4 text-center w-full h-full ${className}`}>

      {title && (
      <HeadingMedium className="font-semibold text-neutral-1100 text-left w-full" as={as}>
        {title}
      </HeadingMedium>
      )}
      {points && (
      <div className="text-neutral-800 text-left">
        {points?.map((point, idx) => (
          <div className="flex gap-2 mb-2" key={idx}>
            {pointIcon}
            <BodyLarge><span className="font-bold text-neutral-1100">{point.title}</span> - {point.description}</BodyLarge>
          </div>
        ))}
      </div>
      )}
    </div>
  );
}
