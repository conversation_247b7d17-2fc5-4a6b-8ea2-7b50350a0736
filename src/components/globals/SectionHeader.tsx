import { BodyLarge, HeadingXLarge } from "../UI/Typography";
import PillBadge from "./PillBadge";
import { useState } from "react";

const SectionHeader = ({
  pill,
  heading,
  subheading,
  component,
  pillComponent,
  className,
}: {
  pill?: string;
  heading: string;
  subheading: string;
  component?: "h1" | "h2";
  pillComponent?: "h2" | "h3" | "p";
  className?: string;
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Count words in subheading
  const wordCount = subheading.trim().split(/\s+/).length;
  const shouldShowReadMore = wordCount > 15;

  // Split text for mobile read more functionality
  const words = subheading.trim().split(/\s+/);
  const truncatedText = words.slice(0, 15).join(" ");
  const remainingText = words.slice(15).join(" ");

  const displayText =
    shouldShowReadMore && !isExpanded ? truncatedText : subheading;

  return (
    <div
      className={`flex flex-col items-center gap-2 md:gap-3 mx-auto mb-6 ${className}`}
    >
      {pill && <PillBadge pill={pill} as={pillComponent} />}
      <HeadingXLarge className="text-center text-neutral-1100" as={component}>
        {heading}
      </HeadingXLarge>
        <div className="text-center">
          {/* Desktop: Always show full text */}
          <BodyLarge className="text-neutral-1100 hidden md:block">
            {subheading}
          </BodyLarge>

          {/* Mobile: Show truncated or full text based on state */}
          <BodyLarge className="text-neutral-1100 md:hidden">
            {displayText}
            {shouldShowReadMore && !isExpanded && <span>...</span> }
          </BodyLarge>
          
          {shouldShowReadMore && (
          <span
            onClick={() => setIsExpanded(!isExpanded)}
            className="md:hidden text-primary-800 hover:text-primary-600 text-sm font-medium ml-1 mt-1"
          >
            {isExpanded ? "Read less" : "Read more"}
          </span>
          )}
        </div>
    </div>
  );
};

export default SectionHeader;
