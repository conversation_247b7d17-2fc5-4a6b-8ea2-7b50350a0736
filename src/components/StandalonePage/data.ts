const data = {
  data: {
    site_standalone_page: [
      {
        id: "page_001",
        hero_title: "Comprehensive Health Insurance Plans",
        hero_description:
          "Protect yourself and your loved ones with the best-in-class health insurance plans tailored for every need.",
        hero_image_url: "https://cdn.oasr.in/oa-site/cms-uploads/media/thumbnail_Niva_health_insurance_logo_a32bd384cc.png",
        pill_content: "Trusted by 1M+ customers",
        slug: "comprehensive-health-insurance",

        standalone_benefits_section: {
          id: "benefits_001",
          pill_content: "Why Choose Us",
          section_description:
            "Explore the top benefits of choosing our health insurance.",
          section_title: "Key Benefits",
          standalone_benefits_section_points: [
            {
              id: "bsp_001",
              title: "Cashless Hospitals",
              description:
                "Access 10,000+ hospitals with cashless claim facilities.",
              icon_url: "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019999ba-4135-7b08-a010-d31de4a359bd/Group%201000001550.svg",
            },
            {
              id: "bsp_002",
              title: "24/7 Support",
              description:
                "Round-the-clock assistance for all insurance needs.",
              icon_url: "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019999ba-4135-7b08-a010-d31de4a359bd/Group%201000001550.svg",
            },
            {
              id: "bsp_003",
              title: "24/7 Support",
              description:
                "Round-the-clock assistance for all insurance needs.",
              icon_url: "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019999ba-4135-7b08-a010-d31de4a359bd/Group%201000001550.svg",
            },
          ],
        },

        standalone_claim_settlement_section: {
          id: "claims_001",
          section_title: "Hassle-free Claims",
          section_description:
            "Get your claims settled quickly and transparently.",
          pill_content: "95% Claim Settlement Ratio",
          standalone_claim_settlement_types: [
            {
              id: "claim_type_001",
              title: "Cashless Claim",
              type: "cashless",
              standalone_claim_settlement_steps: [
                {
                  id: "cstep_001",
                  title: "Hospitalization",
                  description: "Get admitted to a network hospital.",
                },
                {
                  id: "cstep_002",
                  title: "Approval",
                  description: "Insurer validates the claim.",
                },
                {
                  id: "cstep_003",
                  title: "Settlement",
                  description: "Bill settled directly with hospital.",
                },
                {
                  id: "cstep_004",
                  title: "Hospitalization",
                  description: "Get admitted to a network hospital.",
                },
                {
                  id: "cstep_005",
                  title: "Approval",
                  description: "Insurer validates the claim.",
                },
                {
                  id: "cstep_006",
                  title: "Settlement",
                  description: "Bill settled directly with hospital.",
                },
              ],
            },
            {
              id: "claim_type_001",
              title: "Reimbursement Claim",
              type: "cashless",
              standalone_claim_settlement_steps: [
                {
                  id: "cstep_001",
                  title: "Reimbursement title 1",
                  description: "Get admitted to a network hospital.",
                },
                {
                  id: "cstep_002",
                  title: "Reimbursement title 2",
                  description: "Insurer validates the claim.",
                },
                {
                  id: "cstep_003",
                  title: "Reimbursement title 3",
                  description: "Bill settled directly with hospital.",
                },
                {
                  id: "cstep_004",
                  title: "Reimbursement title 1",
                  description: "Get admitted to a network hospital.",
                },
                {
                  id: "cstep_005",
                  title: "Reimbursement title 2",
                  description: "Insurer validates the claim.",
                },
                {
                  id: "cstep_006",
                  title: "Reimbursement title 3",
                  description: "Bill settled directly with hospital.",
                },
              ],
            },
            
          ],
        },

        standalone_documents_section: {
          id: "docs_001",
          pill_content: "Keep Documents Handy",
          section_title: "Required Documents",
          section_description: "Documents you’ll need while filing claims.",
          standalone_documents_section_points: [
            {
              id: "doc_001",
              title: "ID Proof",
              description: "Government issued photo ID.",
              icon_url: "",
            },
            {
              id: "doc_002",
              title: "Medical Reports",
              description: "Prescriptions and test results.",
              icon_url: "",
            },
            {
              id: "doc_003",
              title: "ID Proof",
              description: "Government issued photo ID.",
              icon_url: "",
            },
            {
              id: "doc_004",
              title: "Medical Reports",
              description: "Prescriptions and test results.",
              icon_url: "",
            },
          ],
        },

        standalone_faq_section: {
          id: "faq_001",
          section_title: "Frequently Asked Questions",
          section_description: "Find answers to common queries.",
          pill_content: "Clear Your Doubts",
          standalone_faq_section_points: [
            {
              id: "faq_q1",
              question: "What is cashless hospitalization?",
              answer: "It allows you to get treatment without paying upfront.",
            },
            {
              id: "faq_q2",
              question: "Can I add family members?",
              answer: "Yes, you can include spouse, children, and parents.",
            },
            {
              id: "faq_q3",
              question: "What is cashless hospitalization?",
              answer: "It allows you to get treatment without paying upfront.",
            },
            {
              id: "faq_q4",
              question: "Can I add family members?",
              answer: "Yes, you can include spouse, children, and parents.",
            },
            {
              id: "faq_q5",
              question: "What is cashless hospitalization?",
              answer: "It allows you to get treatment without paying upfront.",
            },
            {
              id: "faq_q6",
              question: "Can I add family members?",
              answer: "Yes, you can include spouse, children, and parents.",
            },
            {
              id: "faq_q7",
              question: "What is cashless hospitalization?",
              answer: "It allows you to get treatment without paying upfront.",
            },
            {
              id: "faq_q8",
              question: "Can I add family members?",
              answer: "Yes, you can include spouse, children, and parents.",
            },
            {
              id: "faq_q9",
              question: "What is cashless hospitalization?",
              answer: "It allows you to get treatment without paying upfront.",
            },
            {
              id: "faq_q10",
              question: "Can I add family members?",
              answer: "Yes, you can include spouse, children, and parents.",
            },
            
          ],
        },

        standalone_inclusion_section: {
          id: "inclusion_001",
          pill_content: "Coverage Details",
          section_title: "What’s Included?",
          section_description: "See what’s covered under your policy.",
          standalone_inclusion_section_points: [
            {
              id: "inc_001",
              title: "Pre & Post Hospitalization",
              type: "inclusion",
              description:
                "Covers medical expenses before and after hospitalization.",
            },
            {
              id: "inc_002",
              title: "Ambulance Charges",
              type: "inclusion",
              description: "Covers emergency ambulance services.",
            },
            {
              id: "inc_001",
              title: "Pre & Post Hospitalization",
              type: "inclusion",
              description:
                "Covers medical expenses before and after hospitalization.",
            },
            {
              id: "inc_002",
              title: "Ambulance Charges",
              type: "exclusion",
              description: "Covers emergency ambulance services.",
            },
            {
              id: "inc_001",
              title: "Pre & Post Hospitalization",
              type: "exclusion",
              description:
                "Covers medical expenses before and after hospitalization.",
            },
            {
              id: "inc_002",
              title: "Ambulance Charges",
              type: "exclusion",
              description: "Covers emergency ambulance services.",
            },
            
          ],
        },

        standalone_insurance_category_section: {
          id: "category_001",
          pill_content: "Plan Categories",
          section_title: "Choose Your Insurance Type",
          section_description: "Pick a plan type that suits your lifestyle.",
          standalone_insurance_category_section_cards: [
            {
              id: "cat_card_002",
              most_popular: false,
              title: "Family Floater",
              points: ["One plan for the whole family"],
              icon_url: "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019999ba-4135-7b08-a010-d31de4a359bd/Group%201000001550.svg",
            },
            {
              id: "cat_card_001",
              most_popular: true,
              title: "Individual Plan",
              points: ["Ideal for single policyholders"],
              icon_url: "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019999ba-4135-7b08-a010-d31de4a359bd/Group%201000001550.svg",
            },
            {
              id: "cat_card_003",
              most_popular: false,
              title: "Family Floater",
              points: ["One plan for the whole family"],
              icon_url: "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019999ba-4135-7b08-a010-d31de4a359bd/Group%201000001550.svg",
            },
          ],
        },

        standalone_key_factors_section: {
          id: "keyfactors_001",
          pill_content: "Things That Matter",
          section_title: "Key Factors",
          section_description: "Important aspects before buying a plan.",
          standalone_key_factors_section_points: [
            {
              id: "kf_001",
              title: "Premium",
              description: "Affordable and flexible premium options.",
              icon_url: "",
            },
            {
              id: "kf_002",
              title: "Premium",
              description: "Affordable and flexible premium options.",
              icon_url: "",
            },
            {
              id: "kf_003",
              title: "Premium",
              description: "Affordable and flexible premium options.",
              icon_url: "",
            },
            {
              id: "kf_004",
              title: "Premium",
              description: "Affordable and flexible premium options.",
              icon_url: "",
            },
            
          ],
        },

        standalone_plans_section: {
          id: "plans_001",
          section_title: "Our Plans",
          section_description: "Compare and choose the right plan.",
          pill_content: "Best Value Plans",
          standalone_plans_section_plans: [
            {
              id: "plan_001",
              health_product_variant: {
                id: "variant_001",
                variant_name: "Smart Health Protect",
                variant_slug: "smart-health-protect",
                temp_slug: "shp-001",
                product: {
                  insurer: {
                    id: "insurer_001",
                    name: "SafeLife Insurance",
                    logo_url: "https://cdn.oasr.in/oa-site/cms-uploads/media/thumbnail_Niva_health_insurance_logo_a32bd384cc.png",
                    slug: "safelife",
                    temp_slug: "safe-001",
                  },
                },
              },
            },
            {
              id: "plan_002",
              health_product_variant: {
                id: "variant_001",
                variant_name: "Smart Health Protect",
                variant_slug: "smart-health-protect",
                temp_slug: "shp-002",
                product: {
                  insurer: {
                    id: "insurer_001",
                    name: "SafeLife Insurance",
                    logo_url: "https://cdn.oasr.in/oa-site/cms-uploads/media/thumbnail_Niva_health_insurance_logo_a32bd384cc.png",
                    slug: "safelife",
                    temp_slug: "safe-001",
                  },
                },
              },
            },
            {
              id: "plan_003",
              health_product_variant: {
                id: "variant_001",
                variant_name: "Smart Health Protect",
                variant_slug: "smart-health-protect",
                temp_slug: "shp-002",
                product: {
                  insurer: {
                    id: "insurer_001",
                    name: "SafeLife Insurance",
                    logo_url: "https://cdn.oasr.in/oa-site/cms-uploads/media/thumbnail_Niva_health_insurance_logo_a32bd384cc.png",
                    slug: "safelife",
                    temp_slug: "safe-001",
                  },
                },
              },
            },
            
          ],
        },

        standalone_renewal_section: {
          id: "renewal_001",
          pill_content: "Stay Protected",
          section_title: "Renew Your Policy",
          section_description: "Continue enjoying benefits with easy renewals.",
          standalone_renewal_types: [
            {
              id: "renew_type_001",
              title: "Online Renewal",
              type: "online",
              standalone_renewal_steps: [
                {
                  id: "rstep_001",
                  title: "Login",
                  description: "Access your policy account.",
                },
                {
                  id: "rstep_002",
                  title: "Payment",
                  description: "Pay the renewal premium.",
                },
              ],
            },
            {
              id: "renew_type_001",
              title: "Offline Renewal",
              type: "offline",
              standalone_renewal_steps: [
                {
                  id: "rstep_001",
                  title: "visti Branch",
                  description: "Access your policy account.",
                },
                {
                  id: "rstep_002",
                  title: "Payment",
                  description: "Pay the renewal premium.",
                },
              ],
            },
          ],
        },

        standalone_tax_advantage_section: {
          id: "tax_001",
          pill_content: "Save Taxes",
          section_title: "Tax Benefits",
          section_description: "Enjoy tax deductions on premiums paid.",
          standalone_tax_advantage_section_points: [
            {
              id: "taxp_001",
              title: "Section 80D",
              description: "Deduction up to ₹25,000 on premiums.",
              icon_url: "",
            },
            {
              id: "taxp_002",
              title: "Section 80D",
              description: "Deduction up to ₹25,000 on premiums.",
              icon_url: "",
            },
            {
              id: "taxp_003",
              title: "Section 80D",
              description: "Deduction up to ₹25,000 on premiums.",
              icon_url: "",
            },
            {
              id: "taxp_004",
              title: "Section 80D",
              description: "Deduction up to ₹25,000 on premiums.",
              icon_url: "",
            },
            
          ],
        },

        standalone_testimonial_section: {
          id: "testimonials_001",
          pill_content: "Happy Customers",
          section_title: "What People Say",
          section_description: "See how we’ve helped our customers.",
          standalone_testimonial_section_points: [
            {
              id: "testi_001",
              title: "Rajesh Kumar",
              description: "Quick claim process and excellent support!",
            },
            {
              id: "testi_002",
              title: "Ananya Singh",
              description: "Affordable premiums with great benefits.",
            },
            {
              id: "testi_003",
              title: "Rajesh Kumar",
              description: "Quick claim process and excellent support!",
            },
            {
              id: "testi_004",
              title: "Ananya Singh",
              description: "Affordable premiums with great benefits.",
            },
            
          ],
        },

        standalone_verdict_section: {
          id: "verdict_001",
          pill_content: "Our Take",
          section_title: "Final Verdict",
          section_description: "Here’s what we think about this plan.",
          verdict: "Great option for families looking for comprehensive cover.",
          standalone_verdict_section_pros_cons: [
            {
              id: "vc_001",
              points: "Wide hospital network",
              title: "Pros",
              type: "pro",
            },
            {
              id: "vc_002",
              points: "Premiums slightly higher",
              title: "Cons",
              type: "con",
            },
            {
              id: "vc_003",
              points: "Wide hospital network",
              title: "Pros",
              type: "pro",
            },
            {
              id: "vc_004",
              points: "Premiums slightly higher",
              title: "Cons",
              type: "con",
            },
            {
              id: "vc_005",
              points: "Wide hospital network",
              title: "Pros",
              type: "pro",
            },
            {
              id: "vc_006",
              points: "Premiums slightly higher",
              title: "Cons",
              type: "con",
            },
          ],
        },

        standalone_what_to_look_for_section: {
          id: "lookfor_001",
          pill_content: "Be Aware",
          section_description: "Things you should check before buying.",
          section_title: "What to Look For",
          standalone_what_to_look_for_section_points: [
            {
              id: "wl_001",
              title: "Waiting Period",
              description:
                "Check the waiting period for pre-existing conditions.",
              icon_url: "",
            },
            {
              id: "wl_002",
              title: "Waiting Period",
              description:
                "Check the waiting period for pre-existing conditions.",
              icon_url: "",
            },
            {
              id: "wl_003",
              title: "Waiting Period",
              description:
                "Check the waiting period for pre-existing conditions.",
              icon_url: "",
            },
            {
              id: "wl_004",
              title: "Waiting Period",
              description:
                "Check the waiting period for pre-existing conditions.",
              icon_url: "",
            },
            
          ],
        },

        standalone_why_plans_section: {
          id: "why_001",
          pill_content: "Why This Plan?",
          section_title: "Reasons to Choose",
          section_description: "Why customers prefer this plan.",
          standalone_why_plans_section_points: [
            {
              id: "wp_001",
              title: "Affordable Premiums",
              description: "Low-cost coverage with high benefits.",
              icon_url: "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019999ba-4135-7b08-a010-d31de4a359bd/Group%201000001550.svg",
            },
            {
              id: "wp_002",
              title: "Affordable Premiums",
              description: "Low-cost coverage with high benefits.",
              icon_url: "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019999ba-4135-7b08-a010-d31de4a359bd/Group%201000001550.svg",
            },
            {
              id: "wp_003",
              title: "Affordable Premiums",
              description: "Low-cost coverage with high benefits.",
              icon_url: "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019999ba-4135-7b08-a010-d31de4a359bd/Group%201000001550.svg",
            },
            
          ],
        },
      },
    ],
  },
};

export default data;
