import PointCardWithTitle from "@/components/globals/DSComponentsV0/PointCardWithTitle";
import SectionHeader from "@/components/globals/DSComponentsV0/SectionHeaderWithParse";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import SectionContainerSmall from "@/components/globals/SectionContainerSmall";
import { Grid } from "@/components/UI/Grid";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";
import {
  IoMdCheckmarkCircleOutline,
  IoMdCloseCircleOutline,
} from "react-icons/io";

type Point = {
  id: string;
  title: string;
  description: string;
};

const InclusionExclusionSection = ({
  heading,
  subheading,
  pill,
  inclusions,
  exclusions,
  id,
}: {
  heading: string;
  subheading?: string;
  pill?: string;
  inclusions: {
    title: string;
    points: Point[];
  };
  exclusions: {
    title: string;
    points: Point[];
  };
  id?: string;
}) => {
  return (
    <SectionContainerLarge className="!px-0" id={id}>
      <SectionHeader
        pill={pill}
        heading={heading}
        subheading={subheading}
        component="h2"
      />

      <SectionContainerSmall className="!px-0">
        <Grid cols={2} className="hidden md:grid" gapX={8}>
          <PointCardWithTitle
            title={inclusions.title}
            points={inclusions.points.map((point) => ({
              title: point.title,
              description: point.description,
            }))}
            pointIcon={
              <IoMdCheckmarkCircleOutline
                className="text-secondary-400 flex-shrink-0 mt-1"
                size={16}
              />
            }
            className="!px-6 !py-5"
            as="h3"
          />
          <PointCardWithTitle
            title={exclusions.title}
            points={exclusions.points.map((point) => ({
              title: point.title,
              description: point.description,
            }))}
            pointIcon={
              <IoMdCloseCircleOutline
                className="text-red-400 flex-shrink-0 mt-1"
                size={16}
              />
            }
            className="!px-6 !py-5"
            as="h3"
          />
        </Grid>

				<MobileCarousel totalSlides={2} className="md:hidden">
          <MobileCarouselItem>
						<PointCardWithTitle
              title={inclusions.title}
              points={inclusions.points.map((point) => ({
                title: point.title,
                description: point.description,
              }))}
              pointIcon={
                <IoMdCheckmarkCircleOutline
                  className="text-secondary-400 flex-shrink-0 mt-1"
                  size={16}
                />
              }
              className="!px-6 !py-5"
              as="h3"
            />
					</MobileCarouselItem>
          <MobileCarouselItem>
						<PointCardWithTitle
              title={exclusions.title}
              points={exclusions.points.map((point) => ({
                title: point.title,
                description: point.description,
              }))}
              pointIcon={
                <IoMdCloseCircleOutline
                  className="text-red-400 flex-shrink-0 mt-1"
                  size={16}
                />
              }
              className="!px-6 !py-5"
              as="h3"
            />
					</MobileCarouselItem>
        </MobileCarousel>
      </SectionContainerSmall>
    </SectionContainerLarge>
  );
};

export default InclusionExclusionSection;
